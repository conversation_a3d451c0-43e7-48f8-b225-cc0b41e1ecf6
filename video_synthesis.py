import json
import os
import sys

# 设置CUDA内存分配配置 - 必须在导入PyTorch之前设置
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,garbage_collection_threshold:0.8,max_split_size_mb:100'

import hashlib
import re
import time
import tempfile
import shutil
import requests
import random
import cv2
import subprocess
import traceback
from typing import Optional, Tuple
import threading
import queue

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from task_manager import video_task_manager
import service.trans_dh_service

from h_utils.custom import CustomError
from y_utils.config import GlobalConfig
from y_utils.logger import logger
# Configure logging
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# logger = logging.getLogger("video_synthesis")

# Constants
MODELS_BASE_DIR = "models"
TEMP_DIR = "temp"
# 文件名标准化函数

def generate_audio(content: str, task_id: str, task_dir: str, video_path: str) -> str:
    """
    使用 GPT-SoVITS API 生成音频

    Args:
        content: 要生成音频的文本内容
        task_id: 任务ID
        task_dir: 任务目录路径 (temp/[task_id])
        video_path: 视频模型路径，用于获取参考音频

    Returns:
        str: 生成的音频文件路径

    Raises:
        Exception: 当参考音频文件不存在时抛出异常
    """
    try:
        logger.info(f"开始调用 GPT-SoVITS API 生成音频...")
        logger.info(f"输入文本: {content}")
        logger.info(f"任务ID: {task_id}")
        logger.info(f"任务目录: {task_dir}")
        logger.info(f"视频模型路径: {video_path}")

        # 获取模型的参考音频路径 - 与训练时保持一致的路径
        audio_dir = directory = os.path.dirname(video_path)
        ref_audio_path = None

        # 查找已训练的音频文件（与 audio_train 方法保持一致）
        if os.path.exists(audio_dir):
            for file in os.listdir(audio_dir):
                if file.startswith("audio."):
                    ref_audio_path = os.path.join(audio_dir, file)
                    break

        # 如果参考音频不存在，抛出异常并更新任务状态
        if not ref_audio_path or not os.path.exists(ref_audio_path):
            error_msg = f"模型 {audio_dir} 的参考音频文件不存在，请先完成模型训练"
            logger.error(error_msg)

            # 更新任务状态为失败
            from database import get_db, update_task_status
            db = next(get_db())
            try:
                update_task_status(db, task_id, "failed",
                                   error_reason=error_msg)
                logger.info(f"已更新任务 {task_id} 状态为失败")
            except Exception as db_error:
                logger.error(f"更新任务状态失败: {str(db_error)}")
            finally:
                db.close()

            raise Exception(error_msg)

        # 确保使用绝对路径
        abs_ref_audio_path = os.path.abspath(ref_audio_path)
        logger.info(f"找到参考音频文件: {abs_ref_audio_path}")

        # 调用 GPT-SoVITS API
        url = "http://0.0.0.0:11996/tts_url"
        params = {
            "text": content,
            "audio_paths": [ abs_ref_audio_path ]
        }
        logger.info(
            f"API 请求参数: {json.dumps(params, ensure_ascii=False, indent=2)}")

        logger.info("发送 API 请求...")
        response = requests.post(url, json=params)
        logger.info(f"API 响应状态码: {response.status_code}")

        if response.status_code == 200:
            # 保存生成的音频文件到任务目录
            audio_path = os.path.abspath(
                os.path.join(task_dir, "generated_audio.wav"))

            logger.info(f"保存生成的音频到: {audio_path}")

            with open(audio_path, "wb") as f:
                f.write(response.content)

            logger.info("音频文件保存成功")
            return audio_path
        else:
            error_msg = f"GPT-SoVITS API 调用失败: 状态码 {response.status_code}, 响应内容: {response.text}"
            logger.error(error_msg)
            raise Exception(error_msg)

    except requests.exceptions.ConnectionError as e:
        error_msg = f"连接 GPT-SoVITS API 失败: {str(e)}"
        logger.error(error_msg)
        logger.error("请确认 GPT-SoVITS 服务是否在运行于 http://0.0.0.0:11996/tts_url")
        raise Exception(error_msg)
    except Exception as e:
        # 如果是我们主动抛出的异常（参考音频不存在），直接重新抛出
        if "参考音频文件不存在" in str(e):
            raise e

        error_msg = f"生成音频过程中发生错误: {str(e)}"
        logger.error(error_msg)
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise Exception(error_msg)
    
def sanitize_filename(filename):
    """
    标准化文件名，移除特殊字符并保留扩展名。
    """
    # 分离文件名和扩展名
    name, ext = os.path.splitext(filename)
    # 使用正则表达式替换特殊字符为下划线
    sanitized_name = re.sub(r"[^a-zA-Z0-9_-]", "_", name)
    # 返回标准化后的文件名
    return f"{sanitized_name}{ext}"


def write_video(
    output_imgs_queue,
    temp_dir,
    result_dir,
    work_id,
    audio_path,
    result_queue,
    width,
    height,
    fps,
    watermark_switch=0,
    digital_auth=0,
):
    # fps=25
    output_mp4 = os.path.join("./temp", "{}.mp4".format(work_id))
    result_path = os.path.join("./result", "{}.mp4".format(work_id))

    # Ensure output directories exist
    os.makedirs(os.path.dirname(output_mp4), exist_ok=True)
    os.makedirs(os.path.dirname(result_path), exist_ok=True)

    # Try different codecs in order of preference for compatibility
    codecs_to_try = [
        cv2.VideoWriter_fourcc(*"mp4v"),  # MPEG-4 Part 2 (most compatible)
        cv2.VideoWriter_fourcc(*"XVID"),  # Xvid MPEG-4
        cv2.VideoWriter_fourcc(*"MJPG"),  # Motion JPEG (fallback)
        cv2.VideoWriter_fourcc(*"X264"),  # H.264 alternative
    ]

    video_write = None
    for fourcc in codecs_to_try:
        video_write = cv2.VideoWriter(output_mp4, fourcc, fps, (width, height))
        if video_write.isOpened():
            logger.info(
                f"Successfully initialized VideoWriter with fourcc: {fourcc}")
            break
        else:
            video_write.release()

    try:
        if video_write is None or not video_write.isOpened():
            logger.warning(
                f"OpenCV VideoWriter failed for {work_id}, falling back to FFmpeg-based approach")
            # Fallback: save frames as images and use FFmpeg to create video
            frames_dir = os.path.join("./temp", f"{work_id}_frames")
            os.makedirs(frames_dir, exist_ok=True)

            frame_count = 0
            while True:
                state, reason, value_ = output_imgs_queue.get()
                if type(state) == bool and state == True:
                    logger.info(f"Custom VideoWriter [{work_id}]视频帧队列处理已结束")
                    break
                else:
                    if type(state) == bool and state == False:
                        logger.error(
                            f"Custom VideoWriter [{work_id}]任务视频帧队列 -> 异常原因:[{reason}]")
                        raise CustomError(reason)

                    # Save frames as images
                    for result_img in value_:
                        frame_path = os.path.join(
                            frames_dir, f"frame_{frame_count:06d}.jpg")
                        cv2.imwrite(frame_path, result_img)
                        frame_count += 1
                    logger.info(f"更新任务状态 任务id:{work_id}")
                    video_task_manager.update_task(
                        work_id,
                        status='processing',
                        code=302,
                        title='合成中',
                        msg='合成中',
                        time=str(int(time.time()))
                    )

            # Create video from frames using FFmpeg
            if frame_count > 0:
                ffmpeg_cmd = f"ffmpeg -loglevel warning -y -r {fps} -i {frames_dir}/frame_%06d.jpg -c:v libx264 -preset fast -crf 18 -pix_fmt yuv420p {output_mp4}"
                logger.info(f"Creating video from frames: {ffmpeg_cmd}")
                result = subprocess.run(
                    ffmpeg_cmd, shell=True, check=True, capture_output=True, text=True)
                logger.info("Successfully created video from frames")

                # Clean up frame files
                import shutil
                shutil.rmtree(frames_dir, ignore_errors=True)
            else:
                logger.error(f"No frames to process for {work_id}")
                result_queue.put([False, f"没有帧数据可处理"])
                return
        else:
            print("Custom VideoWriter init done")
            frame_buffer = []
            buffer_size = 10  # Buffer frames for batch writing

            while True:
                state, reason, value_ = output_imgs_queue.get()
                if type(state) == bool and state == True:
                    logger.info(
                        "Custom VideoWriter [{}]视频帧队列处理已结束".format(work_id)
                    )

                    # Write any remaining frames in buffer
                    if frame_buffer:
                        for buffered_frame in frame_buffer:
                            video_write.write(buffered_frame)
                        frame_buffer.clear()

                    logger.info(
                        "Custom VideoWriter Silence Video saved in {}".format(
                            os.path.realpath(output_mp4)
                        )
                    )
                    break
                else:
                    if type(state) == bool and state == False:
                        logger.error(
                            "Custom VideoWriter [{}]任务视频帧队列 -> 异常原因:[{}]".format(
                                work_id, reason
                            )
                        )
                        raise CustomError(reason)

                    # Add frames to buffer for batch processing
                    for result_img in value_:
                        frame_buffer.append(result_img)

                        # Write buffer when it reaches the buffer size
                        if len(frame_buffer) >= buffer_size:
                            for buffered_frame in frame_buffer:
                                video_write.write(buffered_frame)
                            frame_buffer.clear()
                    logger.info(f"更新任务状态 任务id:{work_id}")
                    video_task_manager.update_task(
                        work_id,
                        status='processing',
                        code=302,
                        title='合成中',
                        msg='合成中',
                        time=str(int(time.time()))
                    )
            # Ensure video writer is properly released
            if video_write is not None and video_write.isOpened():
                video_write.release()

        # 检查是否有字幕文件
        subtitle_path = os.path.join(os.path.dirname(audio_path), "subtitle.srt")
        has_subtitle = os.path.exists(subtitle_path)

        if has_subtitle:
            logger.info(f"Custom VideoWriter [{work_id}] 发现字幕文件: {subtitle_path}")

        # Optimize FFmpeg commands for better performance
        if watermark_switch == 1 and digital_auth == 1:
            logger.info(
                "Custom VideoWriter [{}]任务需要水印和数字人标识".format(work_id)
            )
            if has_subtitle:
                # 有字幕、水印和数字人标识的命令
                if width > height:
                    command = 'ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -i {} -i {} -filter_complex "[1:v]subtitles={}[v1];[v1][2:v]overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10[v2];[v2][3:v]overlay=(main_w-overlay_w)-10:10" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}'.format(
                        audio_path,
                        output_mp4,
                        GlobalConfig.instance().watermark_path,
                        GlobalConfig.instance().digital_auth_path,
                        subtitle_path.replace('\\', '/'),
                        result_path,
                    )
                else:
                    command = 'ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -i {} -i {} -filter_complex "[1:v]subtitles={}[v1];[v1][2:v]overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10[v2];[v2][3:v]overlay=(main_w-overlay_w)-10:10" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}'.format(
                        audio_path,
                        output_mp4,
                        GlobalConfig.instance().watermark_path,
                        GlobalConfig.instance().digital_auth_path,
                        subtitle_path.replace('\\', '/'),
                        result_path,
                    )
            else:
                # 只有水印和数字人标识的原始命令
                if width > height:
                    command = 'ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10,overlay=(main_w-overlay_w)-10:10" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}'.format(
                        audio_path,
                        output_mp4,
                        GlobalConfig.instance().watermark_path,
                        GlobalConfig.instance().digital_auth_path,
                        result_path,
                    )
                else:
                    command = 'ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10,overlay=(main_w-overlay_w)-10:10" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}'.format(
                        audio_path,
                        output_mp4,
                        GlobalConfig.instance().watermark_path,
                        GlobalConfig.instance().digital_auth_path,
                        result_path,
                    )
            logger.info("command:{}".format(command))
        elif watermark_switch == 1 and digital_auth == 0:
            logger.info("Custom VideoWriter [{}]任务需要水印".format(work_id))
            if has_subtitle:
                # 有字幕和水印的命令
                command = 'ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -i {} -filter_complex "[1:v]subtitles={}[v1];[v1][2:v]overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}'.format(
                    audio_path,
                    output_mp4,
                    GlobalConfig.instance().watermark_path,
                    subtitle_path.replace('\\', '/'),
                    result_path,
                )
            else:
                # 只有水印的原始命令
                command = 'ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}'.format(
                    audio_path,
                    output_mp4,
                    GlobalConfig.instance().watermark_path,
                    result_path,
                )
            logger.info("command:{}".format(command))
        elif watermark_switch == 0 and digital_auth == 1:
            logger.info("Custom VideoWriter [{}]任务需要数字人标识".format(work_id))
            if has_subtitle:
                # 有字幕和数字人标识的命令
                if width > height:
                    command = 'ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -i {} -filter_complex "[1:v]subtitles={}[v1];[v1][2:v]overlay=(main_w-overlay_w)-10:10" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}'.format(
                        audio_path,
                        output_mp4,
                        GlobalConfig.instance().digital_auth_path,
                        subtitle_path.replace('\\', '/'),
                        result_path,
                    )
                else:
                    command = 'ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -i {} -filter_complex "[1:v]subtitles={}[v1];[v1][2:v]overlay=(main_w-overlay_w)-10:10" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}'.format(
                        audio_path,
                        output_mp4,
                        GlobalConfig.instance().digital_auth_path,
                        subtitle_path.replace('\\', '/'),
                        result_path,
                    )
            else:
                # 只有数字人标识的原始命令
                if width > height:
                    command = 'ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:10" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}'.format(
                        audio_path,
                        output_mp4,
                        GlobalConfig.instance().digital_auth_path,
                        result_path,
                    )
                else:
                    command = 'ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:10" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}'.format(
                        audio_path,
                        output_mp4,
                        GlobalConfig.instance().digital_auth_path,
                        result_path,
                    )
            logger.info("command:{}".format(command))
        else:
            if has_subtitle:
                # 添加字幕的命令
                command = "ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -vf \"subtitles={}\" -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}".format(
                    audio_path, output_mp4, subtitle_path.replace('\\', '/'), result_path
                )
                logger.info("Custom command with subtitle:{}".format(command))
            else:
                # 无字幕的原始命令
                command = "ffmpeg -loglevel warning -y -threads 0 -i {} -i {} -c:a aac -c:v libx264 -preset fast -crf 18 -movflags +faststart {}".format(
                    audio_path, output_mp4, result_path
                )
                logger.info("Custom command:{}".format(command))

        # Use subprocess.run for better error handling and performance
        result = subprocess.run(command, shell=True,
                                check=True, capture_output=True, text=True)
        logger.info(f"FFmpeg completed successfully: {result.stdout}")

        print("###### Custom Video Writer write over")
        print(f"###### Video result saved in {os.path.realpath(result_path)}")
        result_queue.put([True, result_path])

    except subprocess.CalledProcessError as e:
        logger.error(f"FFmpeg failed: {e.stderr}")
        result_queue.put([False, f"FFmpeg处理失败: {e.stderr}"])
    except Exception as e:
        logger.error(
            "Custom VideoWriter [{}]视频帧队列处理异常结束，异常原因:[{}]".format(
                work_id, e.__str__()
            )
        )
        result_queue.put(
            [
                False,
                "[{}]视频帧队列处理异常结束，异常原因:[{}]".format(
                    work_id, e.__str__()
                ),
            ]
        )
    finally:
        logger.info("Custom VideoWriter 后处理进程结束")


service.trans_dh_service.write_video = write_video


class VideoSynthesizerWorker:
    """
    单个视频合成工作线程，每个线程包含独立的 TransDhTask 和任务队列
    """

    def __init__(self, worker_id):
        """Initialize a single video synthesizer worker."""
        self.worker_id = worker_id
        self.task = service.trans_dh_service.TransDhTask()
        self.task_queue = queue.Queue()
        self.worker_thread = None
        self.running = False

        logger.info(f"VideoSynthesizerWorker {worker_id} initialized")

    def start(self):
        """启动工作线程"""
        if not self.running:
            self.running = True
            self.worker_thread = threading.Thread(target=self.worker_loop, daemon=True)
            self.worker_thread.start()
            logger.info(f"VideoSynthesizerWorker {self.worker_id} started, thread alive: {self.worker_thread.is_alive()}")
            # 等待一小段时间确保线程启动
            import time
            time.sleep(0.1)
            logger.info(f"VideoSynthesizerWorker {self.worker_id} thread status after delay: {self.worker_thread.is_alive()}")

    def stop(self):
        """停止工作线程"""
        self.running = False
        if self.worker_thread and self.worker_thread.is_alive():
            # 向队列中放入停止信号
            self.task_queue.put(None)
            self.worker_thread.join(timeout=5)
            logger.info(f"VideoSynthesizerWorker {self.worker_id} stopped")

    def worker_loop(self):
        """Worker loop for processing synthesis tasks."""
        logger.info(f"VideoSynthesizerWorker {self.worker_id} worker loop started")
        while self.running:
            queue_item = None
            try:
                # 处理新的队列格式，支持音频生成和视频合成
                logger.debug(f"Worker {self.worker_id} waiting for task...")
                queue_item = self.task_queue.get(timeout=1)

                # 检查停止信号
                if queue_item is None:
                    logger.info(f"Worker {self.worker_id} received stop signal")
                    break

                logger.info(f"Worker {self.worker_id} received task: {len(queue_item)} items - {queue_item}")

                if len(queue_item) == 6:
                    # 新格式：包含content，需要先生成音频
                    task_id, audio_path, video_file_path, model_id, callback_url, content = queue_item
                    logger.info(f"Worker {self.worker_id} processing task with audio generation: {task_id}")
                    self.process_synthesis_task_with_audio_generation(
                        task_id, video_file_path, model_id, callback_url, content)
                else:
                    # 旧格式：音频已生成
                    task_id, audio_path, video_file_path, model_id, callback_url = queue_item
                    logger.info(f"Worker {self.worker_id} processing task with existing audio: {task_id}")
                    self.process_synthesis_task(
                        task_id, audio_path, video_file_path, model_id, callback_url)

            except queue.Empty:
                # 超时，继续循环
                continue
            except Exception as e:
                logger.error(f"Error in worker thread {self.worker_id}: {str(e)}")
                logger.error(f"Error details: {traceback.format_exc()}")
                # 如果有任务项，尝试更新任务状态为失败
                if queue_item is not None and len(queue_item) >= 1:
                    try:
                        task_id = queue_item[0]
                        from database import get_db_session, update_task_status
                        db = get_db_session()
                        try:
                            update_task_status(db, task_id, "failed", error_reason=f"Worker处理异常: {str(e)}")
                        finally:
                            db.close()
                    except Exception as db_error:
                        logger.error(f"Failed to update task status: {str(db_error)}")
            finally:
                if queue_item is not None:
                    self.task_queue.task_done()

        logger.info(f"VideoSynthesizerWorker {self.worker_id} worker loop ended")

    def process_video(
        self, audio_file, video_file, watermark=False, digital_auth=False, task_id=None
    ):
        code = task_id
        # 使用与 queue_synthesis_task 一致的目录结构
        temp_dir = os.path.join(TEMP_DIR, code)
        result_dir = "result"
        os.makedirs(temp_dir, exist_ok=True)
        os.makedirs(result_dir, exist_ok=True)

        try:
            logger.info(f"Worker {self.worker_id} 开始处理视频合成，任务ID: {code}")
            logger.info(f"音频文件: {audio_file}")
            logger.info(f"视频文件: {video_file}")
            logger.info(f"临时目录: {temp_dir}")

            cap = cv2.VideoCapture(video_file)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps = 25
            cap.release()

            # 设置任务字典，用于 write_video 方法
            self.task.task_dic[code] = f"{result_dir}/{code}"

            # 调用核心的视频合成逻辑
            logger.info(f"Worker {self.worker_id} 调用 self.task.work() 开始视频合成...")
            self.task.work(audio_file, video_file, code, 0, 0, 0, 1)

            logger.info(f"Worker {self.worker_id} {code} 任务处理返回结果{self.task.task_dic[code]}")

            # 检查处理结果
            if isinstance(self.task.task_dic[code], list) and len(self.task.task_dic[code]) > 3:
                msg = self.task.task_dic[code][3]
            else:
                msg = "处理完成"

            output_video_path = f"result/{code}.mp4"

            # 验证输出文件是否存在
            if os.path.exists(output_video_path):
                logger.info(f"Worker {self.worker_id} 视频合成成功，输出文件: {output_video_path}")
                return output_video_path, msg
            else:
                error_msg = f"视频合成失败，输出文件不存在: {output_video_path}"
                logger.error(f"Worker {self.worker_id} {error_msg}")
                return None, error_msg

        except Exception as e:
            logger.error(f"Worker {self.worker_id} 处理视频时发生错误: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return None, str(e)  # 出错时也返回两个值，msg 是异常信息

    def process_synthesis_task_with_audio_generation(self, task_id: str, video_file_path: str, model_id: str, callback_url: Optional[str] = None, content: str = None) -> None:
        """
        Process a video synthesis task with audio generation in consumer.

        Args:
            task_id: Task ID
            video_file_path: Path to the uploaded video file
            model_id: Model ID for reference
            callback_url: Optional callback URL
            content: Text content for audio generation
        """
        try:
            logger.info(f"Worker {self.worker_id} 开始处理视频合成任务（包含音频生成）: {task_id}")

            # 创建任务目录
            task_dir = os.path.join(TEMP_DIR, task_id)
            os.makedirs(task_dir, exist_ok=True)

            # 第一步：生成音频（在消费者线程中执行）
            logger.info(f"Worker {self.worker_id} 开始生成音频，任务ID: {task_id}")

            # 更新任务状态为音频生成中
            from database import get_db_session, update_task_status
            db = get_db_session()
            try:
                update_task_status(db, task_id, "processing",
                                 error_reason="正在生成音频...")
            finally:
                db.close()

            # 调用音频生成（同步调用，在消费者线程中执行）
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                if not model_id :
                    audio_path = generate_audio(content, task_id, task_dir, video_file_path)
                else:   
                    from management_api import generate_audio_from_text
                    audio_path = loop.run_until_complete(
                        generate_audio_from_text(content, task_id, task_dir, model_id)
                    )
                    logger.info(f"Worker {self.worker_id} 音频生成成功: {audio_path}")
            finally:
                loop.close()

            # 第二步：检查是否需要生成字幕
            db = get_db_session()
            try:
                from database import Task
                task = db.query(Task).filter(Task.task_uuid == task_id).first()
                if task and task.subtitle_option and task.subtitle_option != '无':
                    logger.info(f"Worker {self.worker_id} 开始生成字幕，选项: {task.subtitle_option}")

                    # 生成字幕文件
                    subtitle_path = os.path.join(os.path.dirname(audio_path), "subtitle.srt")

                    # 获取音频时长
                    import subprocess
                    try:
                        result = subprocess.run([
                            'ffprobe', '-v', 'quiet', '-show_entries', 'format=duration',
                            '-of', 'default=noprint_wrappers=1:nokey=1', audio_path
                        ], capture_output=True, text=True, check=True)
                        duration = float(result.stdout.strip())

                        # 调用字幕生成函数
                        from zm.zm import generate_subtitle
                        success = generate_subtitle(
                            original_text=content,
                            duration=duration,
                            output_path=subtitle_path,
                            font_size=24,
                            font_color=task.subtitle_option
                        )

                        if success:
                            logger.info(f"Worker {self.worker_id} 字幕生成成功: {subtitle_path}")
                        else:
                            logger.warning(f"Worker {self.worker_id} 字幕生成失败")

                    except Exception as e:
                        logger.error(f"Worker {self.worker_id} 字幕生成过程出错: {str(e)}")

                # 更新任务状态为视频合成中
                update_task_status(db, task_id, "processing",
                                 error_reason="正在合成视频...")
            finally:
                db.close()

            # 第三步：进行视频合成
            logger.info(f"Worker {self.worker_id} 开始视频合成，音频文件: {audio_path}")
            logger.info(f"视频文件: {video_file_path}")

            # 使用 process_video 方法，它内部调用 self.task.work()
            output_video_path, msg = self.process_video(
                audio_file=audio_path,
                video_file=video_file_path,
                watermark=False,
                digital_auth=False,
                task_id=task_id
            )

            if output_video_path is None:
                # 处理失败
                error_msg = f"视频合成失败: {msg}"
                logger.error(f"Worker {self.worker_id} {error_msg}")

                # Update task status in database
                db = get_db_session()
                try:
                    update_task_status(db, task_id, "failed",
                                       error_reason=error_msg)
                finally:
                    db.close()
                return

            # 处理成功
            logger.info(f"Worker {self.worker_id} 视频合成成功: {output_video_path}")

            # Update task status to completed with download URL
            download_url = f"/video/{task_id}"
            db = get_db_session()
            try:
                update_task_status(db, task_id, "completed", download_url," ")
                logger.info(f"Worker {self.worker_id} Task {task_id} completed successfully")
            finally:
                db.close()

            # Clean up temporary task directory after successful completion
            try:
                if os.path.exists(task_dir):
                    shutil.rmtree(task_dir)
                    logger.info(f"Worker {self.worker_id} Cleaned up temporary directory: {task_dir}")
            except Exception as cleanup_error:
                logger.warning(
                    f"Worker {self.worker_id} Failed to clean up temporary directory {task_dir}: {str(cleanup_error)}")

        except Exception as e:
            logger.error(
                f"Worker {self.worker_id} Error processing synthesis task with audio generation {task_id}: {str(e)}")
            # Update task status to failed
            from database import get_db_session, update_task_status
            db = get_db_session()
            try:
                update_task_status(db, task_id, "failed",
                                   error_reason=f"处理失败: {str(e)}")
            finally:
                db.close()

            # Clean up temporary task directory after failure
            task_dir = os.path.join(TEMP_DIR, task_id)
            try:
                if os.path.exists(task_dir):
                    shutil.rmtree(task_dir)
                    logger.info(
                        f"Worker {self.worker_id} Cleaned up temporary directory after failure: {task_dir}")
            except Exception as cleanup_error:
                logger.warning(
                    f"Worker {self.worker_id} Failed to clean up temporary directory {task_dir}: {str(cleanup_error)}")

    def process_synthesis_task(self, task_id: str, audio_path: str, video_file_path: str, model_id: str, callback_url: Optional[str] = None) -> None:
        """
        Process a video synthesis task using self.task.work() method.

        Args:
            task_id: Task ID
            audio_path: Path to the generated audio file
            video_file_path: Path to the uploaded video file
            model_id: Model ID for reference
            callback_url: Optional callback URL
        """
        try:
            logger.info(f"Worker {self.worker_id} 开始处理视频合成任务: {task_id}")
            logger.info(f"音频文件: {audio_path}")
            logger.info(f"视频文件: {video_file_path}")

            # 使用 process_video 方法，它内部调用 self.task.work()
            output_video_path, msg = self.process_video(
                audio_file=audio_path,
                video_file=video_file_path,
                watermark=False,
                digital_auth=False,
                task_id=task_id
            )

            if output_video_path is None:
                # 处理失败
                error_msg = f"视频合成失败: {msg}"
                logger.error(f"Worker {self.worker_id} {error_msg}")

                # Update task status in database
                from database import get_db_session, update_task_status
                db = get_db_session()
                try:
                    update_task_status(db, task_id, "failed",
                                       error_reason=error_msg)
                finally:
                    db.close()
                return

            # 处理成功
            logger.info(f"Worker {self.worker_id} 视频合成成功: {output_video_path}")

            # Update task status to completed with download URL
            download_url = f"/video/{task_id}"
            from database import get_db_session, update_task_status
            db = get_db_session()
            try:
                update_task_status(db, task_id, "completed", download_url," ")
                logger.info(f"Worker {self.worker_id} Task {task_id} completed successfully")
            finally:
                db.close()

            # Clean up temporary task directory after successful completion
            task_dir = os.path.join(TEMP_DIR, task_id)
            try:
                if os.path.exists(task_dir):
                    shutil.rmtree(task_dir)
                    logger.info(f"Worker {self.worker_id} Cleaned up temporary directory: {task_dir}")
            except Exception as cleanup_error:
                logger.warning(
                    f"Worker {self.worker_id} Failed to clean up temporary directory {task_dir}: {str(cleanup_error)}")

        except Exception as e:
            logger.error(
                f"Worker {self.worker_id} Error processing synthesis task {task_id}: {str(e)}")
            # Update task status to failed
            from database import get_db_session, update_task_status
            db = get_db_session()
            try:
                update_task_status(db, task_id, "failed",
                                   error_reason=f"处理失败: {str(e)}")
            finally:
                db.close()

            # Clean up temporary task directory after failure
            task_dir = os.path.join(TEMP_DIR, task_id)
            try:
                if os.path.exists(task_dir):
                    shutil.rmtree(task_dir)
                    logger.info(
                        f"Worker {self.worker_id} Cleaned up temporary directory after failure: {task_dir}")
            except Exception as cleanup_error:
                logger.warning(
                    f"Worker {self.worker_id} Failed to clean up temporary directory {task_dir}: {str(cleanup_error)}")


class VideoSynthesizer:
    """
    多线程视频合成处理器，管理多个 VideoSynthesizerWorker
    """

    def __init__(self, num_workers=1):
        """Initialize the video synthesizer with specified number of workers."""
        self.num_workers = num_workers
        self.workers = []
        self.current_worker_index = 0

        # Create necessary directories
        os.makedirs(MODELS_BASE_DIR, exist_ok=True)
        os.makedirs(TEMP_DIR, exist_ok=True)

        # Initialize worker threads
        self.start_worker_threads()

        logger.info(f"VideoSynthesizer initialized with {num_workers} workers")

    def start_worker_threads(self):
        """Start worker threads for processing synthesis tasks."""
        for i in range(self.num_workers):
            worker = VideoSynthesizerWorker(worker_id=i)
            worker.start()
            self.workers.append(worker)

        logger.info(f"Started {len(self.workers)} worker threads")

    def stop_all_workers(self):
        """停止所有工作线程"""
        for worker in self.workers:
            worker.stop()
        logger.info("All workers stopped")

    def get_next_worker(self):
        """使用轮询方式获取下一个可用的工作线程"""
        worker = self.workers[self.current_worker_index]
        self.current_worker_index = (self.current_worker_index + 1) % self.num_workers
        return worker

    async def queue_synthesis_task(self, task_id: str, model_id: str, video_file_path: str, content: str = None, audio_file_path: str = None, callback_url: Optional[str] = None) -> None:
        """
        Queue a video synthesis task.

        Args:
            task_id: Task ID
            model_id: Model ID for the reference video (可选，当使用上传音频时)
            video_file_path: Path to the uploaded video file
            content: Text content for audio generation (可选，当使用GPT-SoVITS时)
            audio_file_path: Path to uploaded audio file (可选，当直接使用上传音频时)
            callback_url: Optional callback URL
        """
        # Create task directory
        task_dir = os.path.join(TEMP_DIR, task_id)
        os.makedirs(task_dir, exist_ok=True)

        try:
            logger.info(f"Queueing synthesis task {task_id} for processing")

            # 获取下一个可用的工作线程
            worker = self.get_next_worker()

            if audio_file_path:
                # 使用上传的音频文件，直接进行视频合成
                logger.info(f"Using uploaded audio file: {audio_file_path}, assigned to worker {worker.worker_id}")
                worker.task_queue.put(
                    (task_id, audio_file_path, video_file_path, model_id, callback_url))
            elif content :
                # 使用GPT-SoVITS生成音频，然后进行视频合成
                logger.info(f"Using GPT-SoVITS to generate audio from text, assigned to worker {worker.worker_id}")
                worker.task_queue.put(
                    (task_id, None, video_file_path, model_id, callback_url, content))
            else:
                raise Exception("必须提供音频文件或者文本内容和模型ID")

        except Exception as e:
            logger.error(f"Error queueing task {task_id}: {str(e)}")
            # Update task status in database
            from database import get_db_session, update_task_status
            db = get_db_session()
            try:
                update_task_status(db, task_id, "failed",
                                   error_reason=f"处理失败: {str(e)}")
            finally:
                db.close()












# 全局视频合成器实例
video_synthesizer = None


def get_video_synthesizer(num_workers=1):
    """
    获取全局视频合成器实例，如果不存在则创建

    Args:
        num_workers: 工作线程数量

    Returns:
        VideoSynthesizer: 视频合成器实例
    """
    global video_synthesizer
    if video_synthesizer is None:
        video_synthesizer = VideoSynthesizer(num_workers=num_workers)
    return video_synthesizer


def set_video_synthesizer_workers(num_workers):
    """
    设置视频合成器的工作线程数量

    Args:
        num_workers: 工作线程数量
    """
    global video_synthesizer
    if video_synthesizer is not None:
        # 停止现有的工作线程
        video_synthesizer.stop_all_workers()

    # 创建新的视频合成器实例
    video_synthesizer = VideoSynthesizer(num_workers=num_workers)
    logger.info(f"Video synthesizer configured with {num_workers} workers")
