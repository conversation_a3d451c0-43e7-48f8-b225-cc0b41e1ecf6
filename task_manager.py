import json
import time
import datetime
import threading
import logging
import redis
import os
import re
from typing import Dict, Any, Optional, List
from y_utils.logger import logger

# Configure logging
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# logger = logging.getLogger("task_manager")

class BaseTaskManager:
    """
    Base task manager for handling digital human tasks.
    Provides persistent storage and automatic cleanup of old tasks.
    """

    def __init__(self, redis_host: str = 'localhost', redis_port: int = 6379, redis_db: int = 0, redis_password: Optional[str] = None, key_prefix: str = 'task'):
        """
        Initialize the task manager.

        Args:
            redis_host: Redis server host
            redis_port: Redis server port
            redis_db: Redis database number
            redis_password: Redis password (if required)
            key_prefix: Prefix for Redis keys
        """
        self.redis_host = redis_host
        self.redis_port = redis_port
        self.redis_db = redis_db
        self.redis_password = redis_password
        self.key_prefix = key_prefix
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self.lock = threading.RLock()

        # Initialize Redis connection
        self.redis_client = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            password=redis_password,
            decode_responses=True
        )

        # Test Redis connection
        try:
            self.redis_client.ping()
            logger.info(f"Connected to Redis at {redis_host}:{redis_port}")
        except redis.ConnectionError as e:
            logger.error(f"Failed to connect to Redis: {str(e)}")
            raise

        # Load existing tasks
        self._load_tasks()

        # Start cleanup thread
        self._start_cleanup_thread()

    def _get_redis_key(self, task_id: str) -> str:
        """Get Redis key for a task"""
        return f"{self.key_prefix}:{task_id}"

    def _load_tasks(self):
        """Load tasks from Redis storage"""
        try:
            # Get all task keys
            pattern = f"{self.key_prefix}:*"
            task_keys = self.redis_client.keys(pattern)

            for task_key in task_keys:
                try:
                    task_data_str = self.redis_client.get(task_key)
                    if task_data_str:
                        task_data = json.loads(task_data_str)
                        task_id = task_data.get('task_id')
                        if task_id:
                            # Check if task is older than 1 day
                            created_at = task_data.get('created_at', 0)
                            if time.time() - created_at <= 86400:  # 24 hours in seconds
                                self.tasks[task_id] = task_data
                            else:
                                # Task is too old, mark it as failed if it's not completed
                                if task_data.get('status') not in ['completed', 'failed']:
                                    task_data['status'] = 'failed'
                                    task_data['title'] = self._get_failed_title()
                                    task_data['msg'] = 'no'
                                    task_data['code'] = 400
                                    self._save_task(task_id, task_data)
                                self.tasks[task_id] = task_data
                except Exception as e:
                    logger.error(f"Error loading task from Redis key {task_key}: {str(e)}")
        except Exception as e:
            logger.error(f"Error loading tasks from Redis: {str(e)}")

    def _get_failed_title(self) -> str:
        """Get the title for failed tasks. To be overridden by subclasses."""
        return "处理失败"

    def _save_task(self, task_id: str, task_data: Dict[str, Any]):
        """Save task to Redis storage"""
        try:
            redis_key = self._get_redis_key(task_id)
            task_data_str = json.dumps(task_data, ensure_ascii=False)

            # Save to Redis with 24 hour expiration (86400 seconds)
            self.redis_client.setex(redis_key, 86400, task_data_str)
            logger.debug(f"Task {task_id} saved to Redis with key {redis_key}")
        except Exception as e:
            logger.error(f"Error saving task {task_id} to Redis: {str(e)}")

    def _result_dir_cleanup(self):
        """清理项目根目录下 result 子目录中昨日及更早的文件"""
        try:
            current_time = time.time()
            yesterday_timestamp = current_time - 86400  # 24小时之前
            result_dir = os.path.join(os.getcwd(), 'result')
            if not os.path.exists(result_dir) or not os.path.isdir(result_dir):
                logger.info("result 目录不存在，无需清理")
                return
            cleaned_files = 0
            cleaned_dirs = 0
            for item in os.listdir(result_dir):
                item_path = os.path.join(result_dir, item)
                try:
                    stat = os.stat(item_path)
                    modified_time = stat.st_mtime
                    if modified_time < yesterday_timestamp:
                        if os.path.isfile(item_path):
                            os.remove(item_path)
                            logger.info(f"Removed old result file: {item}")
                            cleaned_files += 1
                        elif os.path.isdir(item_path):
                            # 递归删除整个子目录
                            import shutil
                            shutil.rmtree(item_path)
                            logger.info(f"Removed old result directory: {item}")
                            cleaned_dirs += 1
                except Exception as e:
                    logger.error(f"Error cleaning result item {item}: {str(e)}")
            logger.info(f"Result dir cleanup completed: {cleaned_files} files, {cleaned_dirs} directories removed")
        except Exception as e:
            logger.error(f"Error during result dir cleanup: {str(e)}")

    def _start_cleanup_thread(self):
        """Start a thread to clean up old tasks daily"""
        def cleanup_worker():
            while True:
                try:
                    # Sleep until next day at 00:05 AM
                    now = datetime.datetime.now()
                    tomorrow = now + datetime.timedelta(days=1)
                    next_run = datetime.datetime(
                        year=tomorrow.year,
                        month=tomorrow.month,
                        day=tomorrow.day,
                        hour=0,
                        minute=5
                    )
                    sleep_seconds = (next_run - now).total_seconds()
                    time.sleep(sleep_seconds)

                    # Perform cleanup
                    self._cleanup_old_tasks()
                    self._cleanup_uuid_files_and_dirs()
                    self._result_dir_cleanup()
                except Exception as e:
                    logger.error(f"Error in cleanup thread: {str(e)}")
                    # Sleep for an hour before retrying
                    time.sleep(3600)

        # Start the cleanup thread as daemon
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()

    def _cleanup_old_tasks(self):
        """Clean up tasks older than 1 day"""
        with self.lock:
            current_time = time.time()
            task_ids_to_remove = []

            # Find tasks to remove from memory
            for task_id, task_data in self.tasks.items():
                created_at = task_data.get('created_at', 0)
                if current_time - created_at > 86400:  # 24 hours in seconds
                    task_ids_to_remove.append(task_id)

            # Remove tasks from memory (Redis keys will expire automatically)
            for task_id in task_ids_to_remove:
                try:
                    del self.tasks[task_id]
                    logger.debug(f"Removed expired task {task_id} from memory")
                except Exception as e:
                    logger.error(f"Error removing task {task_id} from memory: {str(e)}")

            # Also clean up any expired keys from Redis (though they should auto-expire)
            try:
                pattern = f"{self.key_prefix}:*"
                task_keys = self.redis_client.keys(pattern)
                redis_cleanup_count = 0

                for task_key in task_keys:
                    try:
                        task_data_str = self.redis_client.get(task_key)
                        if task_data_str:
                            task_data = json.loads(task_data_str)
                            created_at = task_data.get('created_at', 0)
                            if current_time - created_at > 86400:  # 24 hours in seconds
                                self.redis_client.delete(task_key)
                                redis_cleanup_count += 1
                    except Exception as e:
                        logger.error(f"Error checking Redis key {task_key} for cleanup: {str(e)}")

                logger.info(f"Cleaned up {len(task_ids_to_remove)} old tasks from memory and {redis_cleanup_count} from Redis")
            except Exception as e:
                logger.error(f"Error during Redis cleanup: {str(e)}")
                logger.info(f"Cleaned up {len(task_ids_to_remove)} old tasks from memory")

    def _cleanup_uuid_files_and_dirs(self):
        """Clean up UUID format directories and media files older than 1 day"""
        try:
            # Get current time and yesterday's timestamp
            current_time = time.time()
            yesterday_timestamp = current_time - 86400  # 24 hours ago

            # UUID pattern (8-4-4-4-12 format)
            uuid_pattern = re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$', re.IGNORECASE)

            # Media file pattern (UUID_format.ext)
            media_pattern = re.compile(r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}_format\.(mp4|wav|avi|mov|mp3|flac|aac)$', re.IGNORECASE)

            project_root = os.getcwd()  # Get current working directory (project root)

            cleaned_dirs = 0
            cleaned_files = 0

            # Clean up UUID format directories
            for item in os.listdir(project_root):
                item_path = os.path.join(project_root, item)

                # Check if it's a directory with UUID format name
                if os.path.isdir(item_path) and uuid_pattern.match(item):
                    try:
                        # Get directory modification time
                        dir_stat = os.stat(item_path)
                        dir_modified_time = dir_stat.st_mtime

                        # Only clean directories modified yesterday or earlier
                        if dir_modified_time < yesterday_timestamp:
                            # Check if directory is empty
                            if not os.listdir(item_path):
                                os.rmdir(item_path)
                                logger.info(f"Removed empty UUID directory: {item}")
                                cleaned_dirs += 1
                            else:
                                logger.debug(f"UUID directory {item} is not empty, skipping")
                    except Exception as e:
                        logger.error(f"Error processing UUID directory {item}: {str(e)}")

                # Check if it's a media file with UUID_format pattern
                elif os.path.isfile(item_path) and media_pattern.match(item):
                    try:
                        # Get file modification time
                        file_stat = os.stat(item_path)
                        file_modified_time = file_stat.st_mtime

                        # Only clean files modified yesterday or earlier
                        if file_modified_time < yesterday_timestamp:
                            os.remove(item_path)
                            logger.info(f"Removed old UUID media file: {item}")
                            cleaned_files += 1
                    except Exception as e:
                        logger.error(f"Error removing UUID media file {item}: {str(e)}")

            logger.info(f"UUID cleanup completed: {cleaned_dirs} directories and {cleaned_files} media files removed")

        except Exception as e:
            logger.error(f"Error during UUID files and directories cleanup: {str(e)}")

    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get task data by ID.

        Args:
            task_id: Task ID to retrieve

        Returns:
            Task data dictionary or None if not found
        """
        with self.lock:
            task_data = None
            # If not found in memory, try to load from Redis
            
            try:
                redis_key = self._get_redis_key(task_id)
                task_data_str = self.redis_client.get(redis_key)
                if task_data_str:
                    task_data = json.loads(task_data_str)
                    # Add to in-memory cache
                    self.tasks[task_id] = task_data
                    logger.info(f"Loaded task {task_id} from Redis")
            except Exception as e:
                logger.error(f"Error loading task {task_id} from Redis: {str(e)}")

            return task_data

    def update_task(self, task_id: str, **kwargs) -> Optional[Dict[str, Any]]:
        """
        Update task data.

        Args:
            task_id: Task ID to update
            **kwargs: Key-value pairs to update

        Returns:
            Updated task data or None if task not found
        """
        with self.lock:
            # First try to get from memory
            task_data = self.tasks.get(task_id)

            # If not found in memory, try to load from Redis
            if task_data is None:
                try:
                    redis_key = self._get_redis_key(task_id)
                    task_data_str = self.redis_client.get(redis_key)
                    if task_data_str:
                        task_data = json.loads(task_data_str)
                        # Add to in-memory cache
                        self.tasks[task_id] = task_data
                        logger.info(f"Loaded task {task_id} from Redis for update")
                except Exception as e:
                    logger.error(f"Error loading task {task_id} from Redis for update: {str(e)}")

            if not task_data:
                return None

            # Update task data
            task_data.update(kwargs)
            task_data['updated_at'] = int(time.time())

            # Save updated task to memory and Redis
            self.tasks[task_id] = task_data
            self._save_task(task_id, task_data)

            logger.debug(f"Task {task_id} updated successfully")
            return task_data

    def mark_task_failed(self, task_id: str, error_msg: str, code: int = 400) -> Optional[Dict[str, Any]]:
        """
        Mark a task as failed.

        Args:
            task_id: Task ID to update
            error_msg: Error message

        Returns:
            Updated task data or None if task not found
        """
        with self.lock:
            task_data = self.tasks.get(task_id)
            if task_data is not None:  # 确保 task_data 存在
                if task_data.get("code") == 200:
                    return task_data
            current_time = int(time.time())
            result = self.update_task(
                task_id,
                status='failed',
                code=code,
                title=error_msg,
                msg='no',
                time=str(current_time)
            )
            logger.info(f"Task {task_id} marked as failed: {error_msg}")
            return result

    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """Get all tasks from memory and Redis"""
        with self.lock:
            # First, load any tasks from Redis that aren't in memory
            try:
                pattern = f"{self.key_prefix}:*"
                task_keys = self.redis_client.keys(pattern)

                for task_key in task_keys:
                    try:
                        # Extract task_id from Redis key
                        task_id = task_key.replace(f"{self.key_prefix}:", "")
                        if task_id not in self.tasks:
                            task_data_str = self.redis_client.get(task_key)
                            if task_data_str:
                                task_data = json.loads(task_data_str)
                                # Add to in-memory cache if not too old
                                created_at = task_data.get('created_at', 0)
                                if time.time() - created_at <= 86400:  # 24 hours in seconds
                                    self.tasks[task_id] = task_data
                                    logger.debug(f"Loaded task {task_id} from Redis for get_all_tasks")
                    except Exception as e:
                        logger.error(f"Error loading task from Redis key {task_key} for get_all_tasks: {str(e)}")
            except Exception as e:
                logger.error(f"Error loading tasks from Redis for get_all_tasks: {str(e)}")

            return list(self.tasks.values())


class AudioTaskManager(BaseTaskManager):
    """
    Task manager for handling audio training tasks.
    """

    def __init__(self, redis_host: str = 'localhost', redis_port: int = 6379, redis_db: int = 0, redis_password: Optional[str] = None):
        """Initialize the audio task manager."""
        super().__init__(redis_host=redis_host, redis_port=redis_port, redis_db=redis_db, redis_password=redis_password, key_prefix='audio_task')
        logger.info("Audio task manager initialized")

    def _get_failed_title(self) -> str:
        """Get the title for failed tasks."""
        return "视频预处理失败"

    def add_task(self, task_id: str, video_url: str, callback_url: Optional[str] = None, model_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Add a new audio training task.

        Args:
            task_id: Unique ID for the task
            video_url: URL of the video to process
            callback_url: Optional callback URL
            model_id: Optional model ID, will be generated if not provided

        Returns:
            Task data dictionary
        """
        with self.lock:
            current_time = int(time.time())

            task_data = {
                'task_id': task_id,
                'video_url': video_url,
                'callback_url': callback_url,
                'model_id': model_id,
                'status': 'queued',
                'created_at': current_time,
                'updated_at': current_time,
                'code': 302,
                'title': '训练中',
                'msg': 'waiting',
                'time': str(current_time),
                'jg': None,
                'img': None,
                'oss_url': None,
                'img_url': None
            }

            # Save to memory
            self.tasks[task_id] = task_data

            # Save to Redis
            self._save_task(task_id, task_data)

            logger.info(f"Audio task {task_id} added successfully")
            return task_data

    def mark_task_completed(self, task_id: str, model_id: str, img_url: str) -> Optional[Dict[str, Any]]:
        """
        Mark an audio task as completed.

        Args:
            task_id: Task ID to update
            model_id: Model ID for the completed task
            img_url: URL of the generated image

        Returns:
            Updated task data or None if task not found
        """
        with self.lock:
            current_time = int(time.time())
            result = self.update_task(
                task_id,
                status='completed',
                code=200,
                title='训练完成',
                msg='ok',
                time=str(current_time),
                model_id=model_id,
                img=img_url,
                jg='200'
            )
            logger.info(f"Audio task {task_id} marked as completed")
            return result


class VideoTaskManager(BaseTaskManager):
    """
    Task manager for handling video generation tasks.
    """

    def __init__(self, redis_host: str = 'localhost', redis_port: int = 6379, redis_db: int = 0, redis_password: Optional[str] = None):
        """Initialize the video task manager."""
        super().__init__(redis_host=redis_host, redis_port=redis_port, redis_db=redis_db, redis_password=redis_password, key_prefix='video_task')
        logger.info("Video task manager initialized")

    def _get_failed_title(self) -> str:
        """Get the title for failed tasks."""
        return "音频预处理失败"

    def add_task(self, task_id: str, audio_url: str, callback_url: Optional[str] = None, model_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Add a new video generation task.

        Args:
            task_id: Unique ID for the task
            audio_url: URL of the audio to process
            callback_url: Optional callback URL
            model_id: Model ID to use for generation

        Returns:
            Task data dictionary
        """
        with self.lock:
            current_time = int(time.time())

            task_data = {
                'task_id': task_id,
                'audio_url': audio_url,  # Note: using audio_url instead of video_url
                'callback_url': callback_url,
                'model_id': model_id,
                'status': 'queued',
                'created_at': current_time,
                'updated_at': current_time,
                'code': 302,
                'title': '等待中',
                'msg': 'waiting',
                'time': str(current_time),
                'md5': None,
                'seconds': None,
                'jg': None,
                'img_url': None,
                'oss_url': None
            }

            # Save to memory
            self.tasks[task_id] = task_data

            # Save to Redis
            self._save_task(task_id, task_data)

            logger.info(f"Video task {task_id} added successfully")
            return task_data

    def mark_task_completed(self, task_id: str, video_url: str, img_url: Optional[str] = None ,md5: Optional[str] = None, seconds: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """
        Mark a video task as completed.

        Args:
            task_id: Task ID to update
            video_url: URL of the generated video
            img_url: Optional URL of the thumbnail image
            md5: Optional MD5 hash of the generated video file
            seconds: Optional duration of the input audio in seconds

        Returns:
            Updated task data or None if task not found
        """
        with self.lock:
            current_time = int(time.time())
            result = self.update_task(
                task_id,
                status='completed',
                code=200,
                title='合成完成',
                msg='ok',
                time=str(current_time),
                oss_url=video_url,
                img_url=img_url,
                jg='yes',
                md5=md5,
                seconds=seconds
            )
            logger.info(f"Video task {task_id} marked as completed")
            return result


# Create singleton instances
audio_task_manager = AudioTaskManager()
video_task_manager = VideoTaskManager()
task_manager = audio_task_manager  # For backward compatibility
