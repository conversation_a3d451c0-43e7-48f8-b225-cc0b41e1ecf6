import os
import sys
import ffmpeg

# 设置CUDA内存分配配置 - 必须在导入PyTorch之前设置
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,garbage_collection_threshold:0.8,max_split_size_mb:100'

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
import uuid
import time
import shutil
import functools
import json
import traceback
import asyncio
import requests
import configparser
import subprocess
from datetime import datetime
from typing import Optional, List, Dict, Any
from contextlib import asynccontextmanager
from fastapi import FastAPI, Depends, HTTPException, status, Request, Response, Form, File, UploadFile
from fastapi.responses import JSONResponse, FileResponse, RedirectResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from video_synthesis import get_video_synthesizer, set_video_synthesizer_workers
import redis

# Import our modules
from database import (
    get_db, get_db_session, init_database, reset_processing_tasks,
    User, Model, Task, VideoModel, SystemSetting,
    get_user_by_username, create_user, update_user, get_models_by_user, get_all_models,
    create_model, get_tasks_by_user, get_all_tasks, get_tasks_count_by_user, get_all_tasks_count, create_task,
    update_model_status, update_task_status, get_completed_models_by_user, get_all_completed_models,
    get_video_models_by_user, get_all_video_models, get_completed_video_models_by_user, get_all_completed_video_models,
    get_video_models_count_by_user, get_all_video_models_count, create_video_model, get_video_model_by_uuid,
    update_video_model_status, update_video_model_name, delete_video_model,
    get_system_setting, get_all_system_settings, create_or_update_system_setting, delete_system_setting
)
from auth import (
    login_user, get_current_user, get_current_admin_user, get_optional_current_user,
    require_login, require_admin, check_model_permission, check_task_permission
)
try:
    from y_utils.logger import logger
except ImportError:
    from simple_logger import logger

# Import captcha utilities
from captcha_utils import captcha_generator

# Import video synthesis components
try:
    import cv2
    import subprocess
    VIDEO_SYNTHESIS_AVAILABLE = True
except ImportError:
    VIDEO_SYNTHESIS_AVAILABLE = False
    logger.warning("Video synthesis dependencies not available")

# Global variables for video synthesizer
video_synthesizer = None
start_time = None

# Configuration loading
def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()
    config_path = os.path.join("config", "config.ini")

    if os.path.exists(config_path):
        config.read(config_path, encoding='utf-8')
        logger.info(f"配置文件加载成功: {config_path}")
    else:
        logger.warning(f"配置文件不存在: {config_path}，使用默认配置")

    return config

def get_video_synthesis_config():
    """获取视频合成相关配置"""
    config = load_config()

    # 默认配置
    default_config = {
        'num_workers': 1,
        'worker_timeout': 600
    }

    try:
        if config.has_section('VIDEO_SYNTHESIS'):
            num_workers = config.getint('VIDEO_SYNTHESIS', 'num_workers', fallback=default_config['num_workers'])
            worker_timeout = config.getint('VIDEO_SYNTHESIS', 'worker_timeout', fallback=default_config['worker_timeout'])
        else:
            num_workers = default_config['num_workers']
            worker_timeout = default_config['worker_timeout']

        logger.info(f"视频合成配置: num_workers={num_workers}, worker_timeout={worker_timeout}")
        return {
            'num_workers': num_workers,
            'worker_timeout': worker_timeout
        }
    except Exception as e:
        logger.error(f"读取视频合成配置失败: {str(e)}，使用默认配置")
        return default_config


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # Startup
    try:
        # Initialize database
        init_database()

        # Reset processing tasks
        reset_processing_tasks()

        # Create necessary directories
        os.makedirs("audio", exist_ok=True)
        os.makedirs("models", exist_ok=True)
        os.makedirs("result", exist_ok=True)
        os.makedirs("temp", exist_ok=True)
        os.makedirs("static", exist_ok=True)
        os.makedirs("templates", exist_ok=True)

        logger.info("Management system started successfully")
    except Exception as e:
        logger.error(f"Error during startup: {str(e)}")
        raise

    global video_synthesizer
    global start_time

    # 初始化视频合成器（使用新的多线程逻辑）
    try:
        logger.info("正在初始化多线程视频合成器...")

        # 获取配置
        synthesis_config = get_video_synthesis_config()
        num_workers = synthesis_config['num_workers']

        logger.info(f"配置的工作线程数量: {num_workers}")

        # 检查Redis连接
        redis_client = redis.Redis(
            host='localhost', port=6379, db=0, decode_responses=True)
        redis_client.ping()  # 测试连接

        lock_key = "video_synthesizer_start_lock"
        lock_timeout = 30  # 增加超时时间到30秒
        max_retries = 3  # 最大重试次数
        retry_count = 0

        while retry_count < max_retries:
            # 尝试获取锁
            got_lock = redis_client.set(
                lock_key, "1", nx=True, ex=lock_timeout)
            if got_lock:
                try:
                    logger.info("获得锁，开始初始化多线程视频合成器...")

                    # 使用新的多线程视频合成器
                    video_synthesizer = get_video_synthesizer(num_workers=num_workers)
                    start_time = int(time.time())
                    logger.info(f"多线程视频合成器初始化成功，工作线程数: {len(video_synthesizer.workers)}，时间: {start_time}")
                    break
                except Exception as vs_error:
                    logger.error(f"视频合成器初始化失败: {str(vs_error)}")
                    logger.error(f"错误详情: {traceback.format_exc()}")
                    # 即使初始化失败，也要释放锁
                    redis_client.delete(lock_key)
                    raise vs_error
                finally:
                    # 启动完毕后主动释放锁
                    redis_client.delete(lock_key)
            else:
                # 没拿到锁，等待一会再试
                retry_count += 1
                wait_time = 2 + retry_count  # 递增等待时间
                logger.info(
                    f"等待GPU资源，{wait_time}秒后重试 (第{retry_count}/{max_retries}次)")
                time.sleep(wait_time)

        if retry_count >= max_retries:
            logger.warning("达到最大重试次数，跳过视频合成器初始化")
            video_synthesizer = None
            start_time = int(time.time())

    except Exception as e:
        logger.error(f"视频合成器初始化过程中出现错误: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        # 不要因为视频合成器初始化失败而阻止整个应用启动
        video_synthesizer = None
        start_time = int(time.time())

    yield

    # Shutdown
    logger.info("Application shutting down...")


# Create FastAPI app with lifespan
app = FastAPI(title="HeyGem Management System",
              version="1.0.0", lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files and templates
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")


def get_db_with_pool():
    """获取数据库会话（使用线程池管理）"""
    db = get_db_session()
    try:
        yield db
    finally:
        db.close()


# Simple cache for performance optimization
_cache = {}
_cache_timestamps = {}
CACHE_TTL = 30  # 30 seconds cache TTL


def simple_cache(ttl=CACHE_TTL):
    """Simple in-memory cache decorator"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Create cache key from function name and arguments
            cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            current_time = time.time()

            # Check if cached result exists and is still valid
            if (cache_key in _cache and
                cache_key in _cache_timestamps and
                    current_time - _cache_timestamps[cache_key] < ttl):
                return _cache[cache_key]

            # Execute function and cache result
            result = func(*args, **kwargs)
            _cache[cache_key] = result
            _cache_timestamps[cache_key] = current_time

            # Clean old cache entries (simple cleanup)
            if len(_cache) > 100:  # Limit cache size
                old_keys = [k for k, t in _cache_timestamps.items()
                            if current_time - t > ttl * 2]
                for k in old_keys:
                    _cache.pop(k, None)
                    _cache_timestamps.pop(k, None)

            return result
        return wrapper
    return decorator

# Video synthesis functions


# Video synthesis functions


async def generate_audio_from_text(content: str, task_id: str, task_dir: str, model_id: str) -> str:
    """
    使用 GPT-SoVITS API 生成音频

    Args:
        content: 要生成音频的文本内容
        task_id: 任务ID
        task_dir: 任务目录路径 (temp/[task_id])
        model_id: 模型ID，用于获取参考音频

    Returns:
        str: 生成的音频文件路径

    Raises:
        Exception: 当参考音频文件不存在时抛出异常
    """
    try:
        logger.info(f"开始调用 GPT-SoVITS API 生成音频...")
        logger.info(f"输入文本: {content}")
        logger.info(f"任务ID: {task_id}")
        logger.info(f"任务目录: {task_dir}")
        logger.info(f"模型ID: {model_id}")

        # 获取模型的参考音频路径 - 与训练时保持一致的路径
        audio_dir = os.path.join("audio", model_id)
        ref_audio_path = None

        # 查找已训练的音频文件（与 audio_train 方法保持一致）
        if os.path.exists(audio_dir):
            for file in os.listdir(audio_dir):
                if file.startswith("audio."):
                    ref_audio_path = os.path.join(audio_dir, file)
                    break

        # 如果参考音频不存在，抛出异常并更新任务状态
        if not ref_audio_path or not os.path.exists(ref_audio_path):
            error_msg = f"模型 {model_id} 的参考音频文件不存在，请先完成模型训练"
            logger.error(error_msg)

            # 更新任务状态为失败
            from database import get_db, update_task_status
            db = next(get_db())
            try:
                update_task_status(db, task_id, "failed",
                                   error_reason=error_msg)
                logger.info(f"已更新任务 {task_id} 状态为失败")
            except Exception as db_error:
                logger.error(f"更新任务状态失败: {str(db_error)}")
            finally:
                db.close()

            raise Exception(error_msg)

        # 确保使用绝对路径
        abs_ref_audio_path = os.path.abspath(ref_audio_path)
        logger.info(f"找到参考音频文件: {abs_ref_audio_path}")

        # 调用 GPT-SoVITS API
        url = "http://0.0.0.0:11996/tts_url"
        params = {
            "text": content,
            "audio_paths": [ abs_ref_audio_path ]
        }
        logger.info(
            f"API 请求参数: {json.dumps(params, ensure_ascii=False, indent=2)}")

        logger.info("发送 API 请求...")
        response = requests.post(url, json=params)
        logger.info(f"API 响应状态码: {response.status_code}")

        if response.status_code == 200:
            # 保存生成的音频文件到任务目录
            audio_path = os.path.abspath(
                os.path.join(task_dir, "generated_audio.wav"))

            logger.info(f"保存生成的音频到: {audio_path}")

            with open(audio_path, "wb") as f:
                f.write(response.content)

            logger.info("音频文件保存成功")
            return audio_path
        else:
            error_msg = f"GPT-SoVITS API 调用失败: 状态码 {response.status_code}, 响应内容: {response.text}"
            logger.error(error_msg)
            raise Exception(error_msg)

    except requests.exceptions.ConnectionError as e:
        error_msg = f"连接 GPT-SoVITS API 失败: {str(e)}"
        logger.error(error_msg)
        logger.error("请确认 GPT-SoVITS 服务是否在运行于 http://0.0.0.0:11996/tts_url")
        raise Exception(error_msg)
    except Exception as e:
        # 如果是我们主动抛出的异常（参考音频不存在），直接重新抛出
        if "参考音频文件不存在" in str(e):
            raise e

        error_msg = f"生成音频过程中发生错误: {str(e)}"
        logger.error(error_msg)
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise Exception(error_msg)


async def generate_fallback_audio(text: str, task_uuid: str, temp_dir: str) -> str:
    """
    备用音频生成方案（当GPT-SoVITS不可用时）
    """
    audio_path = os.path.join(temp_dir, "generated_audio.wav")

    try:
        # 创建一个简单的音频文件（3秒静音）
        command = f'ffmpeg -f lavfi -i "anullsrc=channel_layout=stereo:sample_rate=22050" -t 3 -c:a pcm_s16le "{audio_path}" -y'
        result = subprocess.run(command, shell=True,
                                capture_output=True, text=True)

        if result.returncode != 0:
            raise Exception(f"备用音频生成失败: {result.stderr}")

        logger.info(f"Generated fallback audio for task {task_uuid}")
        return audio_path

    except Exception as e:
        logger.error(f"Error generating fallback audio: {str(e)}")
        raise


def get_model_video_path(model_uuid: str) -> str:
    """
    Get the reference video path for a model
    """
    # 参考 video_synthesis.py 中的逻辑
    model_dir = os.path.join("models", model_uuid)
    model_video_path = os.path.join(model_dir, "source.mp4")

    # 如果模型目录不存在，创建一个示例视频
    if not os.path.exists(model_video_path):
        os.makedirs(model_dir, exist_ok=True)
        # 创建一个简单的测试视频
        create_sample_video(model_video_path)

    return model_video_path


def create_sample_video(output_path: str):
    """
    Create a sample video for testing
    """
    try:
        # 创建一个简单的测试视频（3秒，640x480，红色背景）
        command = f'ffmpeg -f lavfi -i "color=red:size=640x480:duration=3:rate=25" -c:v libx264 -pix_fmt yuv420p "{output_path}"'
        result = subprocess.run(command, shell=True,
                                capture_output=True, text=True)

        if result.returncode == 0:
            logger.info(f"Created sample video: {output_path}")
        else:
            logger.error(f"Failed to create sample video: {result.stderr}")

    except Exception as e:
        logger.error(f"Error creating sample video: {str(e)}")


def get_audio_duration(audio_path: str) -> float:
    """
    获取音频文件的时长（秒）
    """
    try:
        command = f'ffprobe -v quiet -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "{audio_path}"'
        result = subprocess.run(command, shell=True,
                                capture_output=True, text=True)

        if result.returncode == 0:
            duration = float(result.stdout.strip())
            return duration
        else:
            logger.error(f"Failed to get audio duration: {result.stderr}")
            return 0.0
    except Exception as e:
        logger.error(f"Error getting audio duration: {str(e)}")
        return 0.0


def extract_audio_from_video(video_path: str, output_dir: str, max_duration: float = 10.0) -> str:
    """
    从视频中提取音频，如果视频长度超过max_duration秒则只提取前max_duration秒

    Args:
        video_path: 视频文件路径
        output_dir: 输出目录
        max_duration: 最大音频长度（秒），默认10秒

    Returns:
        提取的音频文件路径
    """
    try:
        logger.info(f"开始从视频提取音频: {video_path}")

        # 首先获取视频时长
        probe_cmd = f'ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "{video_path}"'
        duration = float(subprocess.check_output(probe_cmd, shell=True).decode().strip())
        logger.info(f"视频总时长: {duration}秒")

        # 生成输出音频文件路径
        audio_path = os.path.join(output_dir, "audio.mp3")

        # 如果视频时长超过max_duration秒，只提取前max_duration秒
        if duration > max_duration:
            logger.info(f"视频时长超过{max_duration}秒，将只提取前{max_duration}秒音频")
            command = f'ffmpeg -y -i "{video_path}" -t {max_duration} -vn -acodec mp3 -ab 128k "{audio_path}"'
        else:
            logger.info(f"视频时长在{max_duration}秒内，提取完整音频")
            command = f'ffmpeg -y -i "{video_path}" -vn -acodec mp3 -ab 128k "{audio_path}"'

        logger.info(f"执行命令: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)

        if result.returncode != 0:
            error_msg = f"音频提取失败: {result.stderr}"
            logger.error(error_msg)
            raise Exception(error_msg)

        # 验证生成的音频文件是否存在
        if not os.path.exists(audio_path):
            raise Exception(f"音频文件未生成: {audio_path}")

        # 验证生成的音频时长
        extracted_duration = get_audio_duration(audio_path)
        logger.info(f"提取的音频时长: {extracted_duration}秒")

        logger.info(f"音频提取成功，保存至: {audio_path}")
        return audio_path

    except Exception as e:
        error_msg = f"从视频提取音频时发生错误: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)


def adjust_audio_duration(input_path: str, output_path: str, target_duration: float = 5.0) -> bool:
    """
    调整音频文件时长到指定范围内

    Args:
        input_path: 输入音频文件路径
        output_path: 输出音频文件路径
        target_duration: 目标时长（秒），默认5秒

    Returns:
        bool: 是否成功调整
    """
    try:
        current_duration = get_audio_duration(input_path)

        if current_duration == 0.0:
            logger.error("无法获取音频时长")
            return False

        logger.info(f"原始音频时长: {current_duration:.2f}秒")

        # if 3.0 <= current_duration <= 10.0:
            # 时长在范围内，直接复制
        # logger.info("音频时长在3-10秒范围内，直接使用")
        shutil.copy2(input_path, output_path)
        return True

        # elif current_duration < 3.0:
        #     # 时长太短，需要拼接
        #     logger.info(
        #         f"音频时长{current_duration:.2f}秒太短，需要拼接到{target_duration}秒")
        #     return extend_audio(input_path, output_path, target_duration)

        # else:
        #     # 时长太长，需要截断
        #     logger.info(
        #         f"音频时长{current_duration:.2f}秒太长，需要截断到{target_duration}秒")
        #     return truncate_audio(input_path, output_path, target_duration)

    except Exception as e:
        logger.error(f"调整音频时长时发生错误: {str(e)}")
        return False


def extend_audio(input_path: str, output_path: str, target_duration: float) -> bool:
    """
    通过重复拼接来延长音频
    """
    try:
        current_duration = get_audio_duration(input_path)
        repeat_times = int(target_duration / current_duration) + 1

        # 创建临时文件列表
        temp_list_file = output_path + "_list.txt"

        with open(temp_list_file, 'w') as f:
            for _ in range(repeat_times):
                f.write(f"file '{os.path.abspath(input_path)}'\n")

        # 使用ffmpeg拼接音频
        command = f'ffmpeg -f concat -safe 0 -i "{temp_list_file}" -t {target_duration} -c copy "{output_path}"'
        result = subprocess.run(command, shell=True,
                                capture_output=True, text=True)

        # 清理临时文件
        if os.path.exists(temp_list_file):
            os.remove(temp_list_file)

        if result.returncode == 0:
            logger.info(f"成功将音频延长到{target_duration}秒")
            return True
        else:
            logger.error(f"音频延长失败: {result.stderr}")
            return False

    except Exception as e:
        logger.error(f"延长音频时发生错误: {str(e)}")
        return False


def truncate_audio(input_path: str, output_path: str, target_duration: float) -> bool:
    """
    截断音频到指定时长
    """
    try:
        command = f'ffmpeg -i "{input_path}" -t {target_duration} -c copy "{output_path}"'
        result = subprocess.run(command, shell=True,
                                capture_output=True, text=True)

        if result.returncode == 0:
            logger.info(f"成功将音频截断到{target_duration}秒")
            return True
        else:
            logger.error(f"音频截断失败: {result.stderr}")
            return False

    except Exception as e:
        logger.error(f"截断音频时发生错误: {str(e)}")
        return False


# Pydantic models for API


class LoginRequest(BaseModel):
    username: str
    password: str
    captcha_id: str
    captcha_code: str


class UserCreateRequest(BaseModel):
    username: str
    nickname: str
    password: str
    user_type: str = "user"
    expire_days: int = 30


class UserUpdateRequest(BaseModel):
    nickname: Optional[str] = None
    password: Optional[str] = None
    expire_time: Optional[str] = None  # ISO format datetime string


class ModelCreateRequest(BaseModel):
    model_name: str


class TaskCreateRequest(BaseModel):
    task_name: str
    content: Optional[str] = None
    model_uuid: Optional[str] = None
    video_model_uuid: Optional[str] = None


class AudioTrainRequest(BaseModel):
    model_uuid: str


class VideoModelCreateRequest(BaseModel):
    model_name: str = Field(..., description="形象名称")


class VideoModelUpdateRequest(BaseModel):
    model_name: str = Field(..., description="形象名称")


class VideoTrainRequest(BaseModel):
    model_uuid: str = Field(..., description="形象序列号")


class VideoGenerateRequest(BaseModel):
    task_uuid: str
    model_uuid: Optional[str] = None  # 当使用GPT-SoVITS时必需
    video_model_uuid: str
    content: Optional[str] = None  # 当使用GPT-SoVITS时必需
    use_uploaded_audio: bool = False  # 是否使用上传的音频文件

class VideoGenerateWithAudioRequest(BaseModel):
    task_uuid: str
    video_model_uuid: str
    use_uploaded_audio: bool = True


class SystemSettingRequest(BaseModel):
    setting_key: str = Field(..., description="设置键名")
    setting_value: str = Field(..., description="设置值")
    setting_type: str = Field(default="text", description="设置类型")
    description: Optional[str] = Field(None, description="设置描述")

# Response models


class StandardResponse(BaseModel):
    code: int
    msg: str
    time: str
    model_id: Optional[str] = None
    task_id: Optional[str] = None

# Authentication endpoints


@app.get("/api/captcha")
async def get_captcha():
    """获取验证码"""
    try:
        captcha_id, image_data = captcha_generator.generate_captcha()
        return {
            "code": 200,
            "data": {
                "captcha_id": captcha_id,
                "image": image_data
            }
        }
    except Exception as e:
        logger.error(f"生成验证码失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="验证码生成失败"
        )


@app.post("/api/login")
async def login(request: LoginRequest, response: Response, db: Session = Depends(get_db_with_pool)):
    """用户登录"""
    # 验证验证码
    if not captcha_generator.verify_captcha(request.captcha_id, request.captcha_code):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="验证码错误或已过期"
        )

    access_token, user = login_user(db, request.username, request.password)
    if not access_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )

    # Set cookie
    response.set_cookie(
        key="access_token",
        value=access_token,
        max_age=24*60*60,  # 24 hours
        httponly=True,
        secure=False,  # Set to True in production with HTTPS
        samesite="lax"
    )

    return {
        "code": 200,
        "msg": "登录成功",
        "user_type": user.user_type,
        "nickname": user.nickname
    }


@app.post("/api/logout")
async def logout(response: Response):
    """用户登出"""
    response.delete_cookie(key="access_token")
    return {"code": 200, "msg": "登出成功"}

# User management endpoints


@app.get("/api/users")
async def get_users(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db_with_pool)
):
    """获取用户列表（管理员）"""
    users = db.query(User).offset(skip).limit(limit).all()
    return {
        "code": 200,
        "data": [
            {
                "id": user.id,
                "username": user.username,
                "nickname": user.nickname,
                "user_type": user.user_type,
                "expire_time": user.expire_time.isoformat() if user.expire_time else None,
                "created_time": user.created_time.isoformat(),
                "is_expired": user.is_expired()
            }
            for user in users
        ]
    }


@app.post("/api/users")
async def create_user_api(
    request: UserCreateRequest,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db_with_pool)
):
    """创建用户（管理员）"""
    # Check if username already exists
    existing_user = get_user_by_username(db, request.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )

    user = create_user(
        db, request.username, request.nickname, request.password,
        request.user_type, request.expire_days
    )

    return {
        "code": 200,
        "msg": "用户创建成功",
        "data": {
            "id": user.id,
            "username": user.username,
            "nickname": user.nickname,
            "user_type": user.user_type
        }
    }


@app.put("/api/users/{user_id}")
async def update_user_api(
    user_id: int,
    request: UserUpdateRequest,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db_with_pool)
):
    """更新用户信息（管理员）"""
    from datetime import datetime

    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # Parse expire_time if provided
    expire_time = None
    if request.expire_time:
        try:
            expire_time = datetime.fromisoformat(
                request.expire_time.replace('Z', '+00:00'))
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="过期时间格式错误"
            )

    updated_user = update_user(
        db, user_id, request.nickname, request.password, expire_time
    )

    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    return {
        "code": 200,
        "msg": "用户更新成功",
        "data": {
            "id": updated_user.id,
            "username": updated_user.username,
            "nickname": updated_user.nickname,
            "user_type": updated_user.user_type,
            "expire_time": updated_user.expire_time.isoformat() if updated_user.expire_time else None
        }
    }


@app.delete("/api/users/{user_id}")
async def delete_user(
    user_id: int,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db_with_pool)
):
    """删除用户（管理员）"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    if user.username == "admin":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除管理员账号"
        )

    db.delete(user)
    db.commit()

    return {"code": 200, "msg": "用户删除成功"}

# Model management endpoints


@app.get("/api/models")
async def get_models(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """获取模型列表"""
    if current_user.is_admin():
        models = get_all_models(db, skip, limit)
    else:
        models = get_models_by_user(db, current_user.id, skip, limit)

    return {
        "code": 200,
        "data": [
            {
                "id": model.id,
                "model_name": model.model_name,
                "model_uuid": model.model_uuid,
                "train_status": model.train_status,
                "download_url": model.download_url,
                "error_reason": model.error_reason,
                "created_time": model.created_time.isoformat(),
                "updated_time": model.updated_time.isoformat(),
                "owner": model.owner.nickname if current_user.is_admin() else None
            }
            for model in models
        ]
    }


@app.get("/api/models/completed")
async def get_completed_models(
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """获取已完成的模型列表（用于任务创建）"""
    try:
        if current_user.is_admin():
            # 管理员查看所有已完成的模型，使用优化的数据库函数
            models = get_all_completed_models(db, limit=100)

            models_data = [
                {
                    "model_uuid": model.model_uuid,
                    "model_name": model.model_name,
                    "owner": model.owner.nickname if model.owner else None
                }
                for model in models
            ]
        else:
            # 普通用户只查看自己的已完成模型，使用优化的数据库函数
            models = get_completed_models_by_user(
                db, current_user.id, limit=50)

            models_data = [
                {
                    "model_uuid": model.model_uuid,
                    "model_name": model.model_name,
                    "owner": None
                }
                for model in models
            ]

        return {
            "code": 200,
            "data": models_data
        }

    except Exception as e:
        logger.error(f"Error getting completed models: {str(e)}")
        return {
            "code": 500,
            "data": [],
            "msg": "获取模型列表失败"
        }


@app.post("/api/models")
async def create_model_api(
    request: ModelCreateRequest,
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """创建模型"""
    model = create_model(db, current_user.id, request.model_name)

    return {
        "code": 200,
        "msg": "模型创建成功",
        "data": {
            "id": model.id,
            "model_name": model.model_name,
            "model_uuid": model.model_uuid,
            "train_status": model.train_status
        }
    }


@app.put("/api/models/{model_id}")
async def update_model(
    model_id: int,
    request: ModelCreateRequest,
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """更新模型"""
    model = db.query(Model).filter(Model.id == model_id).first()
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型不存在"
        )

    if not check_model_permission(current_user, model.user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限操作此模型"
        )

    model.model_name = request.model_name
    model.updated_time = datetime.now()
    db.commit()

    return {"code": 200, "msg": "模型更新成功"}


@app.delete("/api/models/{model_id}")
async def delete_model(
    model_id: int,
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """删除模型"""
    model = db.query(Model).filter(Model.id == model_id).first()
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型不存在"
        )

    if not check_model_permission(current_user, model.user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限操作此模型"
        )

    # Delete model files
    model_dir = os.path.join("audio", model.model_uuid)
    if os.path.exists(model_dir):
        shutil.rmtree(model_dir)

    db.delete(model)
    db.commit()

    return {"code": 200, "msg": "模型删除成功"}

# Task management endpoints


def _get_tasks_data_direct(user_id: int, is_admin: bool, skip: int, limit: int, db: Session):
    """Direct function to get tasks data without cache"""
    from database import get_all_tasks, get_tasks_by_user, get_all_tasks_count, get_tasks_count_by_user

    if is_admin:
        tasks = get_all_tasks(db, skip, limit)
        total = get_all_tasks_count(db)
        # 对于管理员，批量构建响应以避免N+1查询
        task_data = []
        for task in tasks:
            task_info = {
                "id": task.id,
                "task_name": task.task_name,
                "task_uuid": task.task_uuid,
                "content": task.content,
                "model_uuid": task.model_uuid,
                "video_model_uuid": task.video_model_uuid,
                "task_status": task.task_status,
                "download_url": task.download_url,
                "error_reason": task.error_reason,
                "created_time": task.created_time.isoformat(),
                "updated_time": task.updated_time.isoformat(),
                "owner": task.owner.nickname if hasattr(task, 'owner') and task.owner else None
            }
            task_data.append(task_info)
    else:
        tasks = get_tasks_by_user(db, user_id, skip, limit)
        total = get_tasks_count_by_user(db, user_id)
        # 对于普通用户，不需要owner信息，简化响应
        task_data = [
            {
                "id": task.id,
                "task_name": task.task_name,
                "task_uuid": task.task_uuid,
                "content": task.content,
                "model_uuid": task.model_uuid,
                "video_model_uuid": task.video_model_uuid,
                "task_status": task.task_status,
                "download_url": task.download_url,
                "error_reason": task.error_reason,
                "created_time": task.created_time.isoformat(),
                "updated_time": task.updated_time.isoformat()
            }
            for task in tasks
        ]

    return {
        "tasks": task_data,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@simple_cache(ttl=30)  # 增加缓存时间到30秒
def _get_tasks_data_cached(user_id: int, is_admin: bool, skip: int, limit: int, cache_key: str):
    """Internal cached function to get tasks data - creates its own DB session"""
    # Create a new database session for this function
    db = next(get_db())
    try:
        return _get_tasks_data_direct(user_id, is_admin, skip, limit, db)
    finally:
        db.close()


@app.get("/api/tasks")
async def get_tasks(
    skip: int = 0,
    limit: int = 50,  # 增加默认限制，减少分页请求
    no_cache: bool = False,  # 添加禁用缓存参数
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """获取任务列表"""

    # 直接查询数据库，不使用缓存
    result = _get_tasks_data_direct(
        current_user.id,
        current_user.is_admin(),
        skip,
        limit,
        db
    )

    return {
        "code": 200,
        "data": result["tasks"],
        "pagination": {
            "total": result["total"],
            "skip": result["skip"],
            "limit": result["limit"],
            "has_more": result["skip"] + result["limit"] < result["total"]
        }
    }


@app.post("/api/tasks")
async def create_task_api(
    request: TaskCreateRequest,
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """创建任务"""
    # Validate model if provided
    if request.model_uuid:
        model = db.query(Model).filter(
            Model.model_uuid == request.model_uuid).first()
        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="指定的模型不存在"
            )

        # Check if user has permission to use this model
        if not current_user.is_admin() and model.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限使用此模型"
            )

        # Check if model is completed
        if model.train_status != 'completed':
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只能选择训练完成的模型"
            )

    task = create_task(db, current_user.id, request.task_name,
                       request.content, request.model_uuid, request.video_model_uuid)

    return {
        "code": 200,
        "msg": "任务创建成功",
        "data": {
            "id": task.id,
            "task_name": task.task_name,
            "task_uuid": task.task_uuid,
            "task_status": task.task_status,
            "model_uuid": task.model_uuid
        }
    }


@app.put("/api/tasks/{task_id}")
async def update_task(
    task_id: int,
    request: TaskCreateRequest,
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """更新任务"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    if not check_task_permission(current_user, task.user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限操作此任务"
        )

    task.task_name = request.task_name
    if request.content is not None:
        task.content = request.content
    task.updated_time = datetime.now()
    db.commit()

    return {"code": 200, "msg": "任务更新成功"}


@app.delete("/api/tasks/{task_id}")
async def delete_task(
    task_id: int,
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """删除任务"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    if not check_task_permission(current_user, task.user_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权限操作此任务"
        )

    # Delete task files
    task_dir = os.path.join("result", task.task_uuid)
    if os.path.exists(task_dir):
        shutil.rmtree(task_dir)

    db.delete(task)
    db.commit()

    return {"code": 200, "msg": "任务删除成功"}


@app.get("/api/tasks/count")
async def get_tasks_count(
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """获取任务数量（轻量级API）"""
    try:
        if current_user.is_admin():
            total = get_all_tasks_count(db)
        else:
            total = get_tasks_count_by_user(db, current_user.id)

        return {
            "code": 200,
            "data": {
                "total": total
            }
        }
    except Exception as e:
        logger.error(f"Error getting tasks count: {str(e)}")
        return {
            "code": 500,
            "msg": "获取任务数量失败"
        }

# Training and generation endpoints


@app.post("/audio_train", response_model=StandardResponse)
async def audio_train(
    model_uuid: str = Form(...),
    audio_file: UploadFile = File(...),
    db: Session = Depends(get_db_with_pool)
):
    """音频训练接口"""
    current_time = int(time.time())

    # Find model by UUID
    model = db.query(Model).filter(Model.model_uuid == model_uuid).first()
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型不存在"
        )

    # Create audio directory
    audio_dir = os.path.join("audio", model_uuid)
    os.makedirs(audio_dir, exist_ok=True)

    # Save original audio file
    file_extension = os.path.splitext(audio_file.filename)[1]
    original_audio_path = os.path.join(audio_dir, f"original{file_extension}")
    final_audio_path = os.path.join(audio_dir, f"audio{file_extension}")

    try:
        # Save uploaded file
        with open(original_audio_path, "wb") as buffer:
            shutil.copyfileobj(audio_file.file, buffer)

        logger.info(f"Original audio file saved to: {original_audio_path}")

        # Check and adjust audio duration
        logger.info("检查音频文件时长...")
        current_duration = get_audio_duration(original_audio_path)

        if current_duration == 0.0:
            raise Exception("无法获取音频文件时长，可能文件格式不支持")

        logger.info(f"音频文件时长: {current_duration:.2f}秒")

        # Adjust audio duration if needed
        # if not (3.0 <= current_duration <= 10.0):
        #     logger.info("音频时长不在3-10秒范围内，开始调整...")

        #     # Update model status to processing
        #     update_model_status(db, model_uuid, "training",
        #                         error_reason="正在调整音频时长...")

        #     # Determine target duration
        #     if current_duration < 3.0:
        #         target_duration = 5.0  # Extend short audio to 5 seconds
        #     else:
        #         target_duration = 8.0  # Truncate long audio to 8 seconds

        #     success = adjust_audio_duration(
        #         original_audio_path, final_audio_path, target_duration)

        #     if not success:
        #         raise Exception("音频时长调整失败")

        #     # Verify adjusted duration
        #     adjusted_duration = get_audio_duration(final_audio_path)
        #     logger.info(f"调整后音频时长: {adjusted_duration:.2f}秒")

        #     if not (3.0 <= adjusted_duration <= 10.0):
        #         raise Exception(f"音频调整后时长{adjusted_duration:.2f}秒仍不在有效范围内")
        # else:
            # Duration is already in range, just copy the file
        logger.info("音频时长在有效范围内，直接使用")
        shutil.copy2(original_audio_path, final_audio_path)

        # Clean up original file
        if os.path.exists(original_audio_path):
            os.remove(original_audio_path)

    except Exception as e:
        logger.error(f"Error processing audio file: {str(e)}")
        # Clean up files on error
        for path in [original_audio_path, final_audio_path]:
            if os.path.exists(path):
                try:
                    os.remove(path)
                except Exception:
                    pass

        update_model_status(db, model_uuid, "failed",
                            error_reason=f"音频处理失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"音频处理失败: {str(e)}"
        )

    # Update model status to training
    update_model_status(db, model_uuid, "training")

    # TODO: 这里应该调用实际的训练逻辑
    # 现在先模拟训练过程
    try:
        # 模拟训练成功
        download_url = f"/models/{model_uuid}"
        update_model_status(db, model_uuid, "completed", download_url," ")
        logger.info(f"Model {model_uuid} training completed")
    except Exception as e:
        error_msg = f"训练失败: {str(e)}"
        update_model_status(db, model_uuid, "failed", error_reason=error_msg)
        logger.error(f"Model {model_uuid} training failed: {str(e)}")

    return StandardResponse(
        code=200,
        msg="ok",
        time=str(current_time),
        model_id=model_uuid
    )


@app.post("/video_gene", response_model=StandardResponse)
async def video_generate(
    model_uuid: str = Form(...),
    video_file: UploadFile = File(...),
    task_uuid: str = Form(...),
    content: str = Form(...),
    db: Session = Depends(get_db_with_pool)
):
    """视频合成接口（使用GPT-SoVITS生成音频）"""
    current_time = int(time.time())

    # Find model by UUID
    model = db.query(Model).filter(Model.model_uuid == model_uuid).first()
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型不存在"
        )

    # Find task by UUID
    task = db.query(Task).filter(Task.task_uuid == task_uuid).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    # Create temp directory for this task
    temp_dir = os.path.join("temp", task_uuid)
    os.makedirs(temp_dir, exist_ok=True)

    # Save video file to temp directory
    video_file_path = os.path.join(temp_dir, "input_video.mp4")
    try:
        with open(video_file_path, "wb") as buffer:
            shutil.copyfileobj(video_file.file, buffer)
        logger.info(f"Video file saved to: {video_file_path}")
    except Exception as e:
        logger.error(f"Error saving video file: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="视频文件保存失败"
        )

    # Update task status to processing
    update_task_status(db, task_uuid, "processing")

    # Check if video synthesizer is available
    if video_synthesizer is None:
        update_task_status(db, task_uuid, "failed", error_reason="视频合成器未初始化")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="视频合成服务暂时不可用"
        )

    # Start processing task in background (使用GPT-SoVITS生成音频)
    try:
        asyncio.create_task(
            video_synthesizer.queue_synthesis_task(
                task_id=task_uuid,
                model_id=model_uuid,
                video_file_path=video_file_path,
                content=content,
                callback_url=""
            )
        )
        logger.info(f"任务 {task_uuid} 已提交到多线程视频合成器")
    except Exception as e:
        logger.error(f"提交任务到视频合成器失败: {str(e)}")
        update_task_status(db, task_uuid, "failed", error_reason=f"任务提交失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"任务提交失败: {str(e)}"
        )

    return StandardResponse(
        code=200,
        msg="ok",
        time=str(current_time),
        task_id=task_uuid
    )


@app.post("/video_gene_with_audio", response_model=StandardResponse)
async def video_generate_with_audio(
    task_uuid: str = Form(...),
    video_file: UploadFile = File(...),
    audio_file: UploadFile = File(...),
    db: Session = Depends(get_db_with_pool)
):
    """视频合成接口（使用上传的音频文件）"""
    current_time = int(time.time())

    # Find task by UUID
    task = db.query(Task).filter(Task.task_uuid == task_uuid).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    # Create temp directory for this task
    temp_dir = os.path.join("temp", task_uuid)
    os.makedirs(temp_dir, exist_ok=True)

    try:
        # Save video file to temp directory
        video_file_path = os.path.join(temp_dir, "input_video.mp4")
        with open(video_file_path, "wb") as buffer:
            shutil.copyfileobj(video_file.file, buffer)
        logger.info(f"Video file saved to: {video_file_path}")

        # Validate and save audio file
        if not audio_file.filename:
            raise HTTPException(status_code=400, detail="未选择音频文件")

        # Check audio file extension
        audio_extension = os.path.splitext(audio_file.filename)[1].lower()
        allowed_audio_extensions = ['.wav', '.mp3', '.m4a', '.aac', '.flac']
        if audio_extension not in allowed_audio_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的音频格式，请上传 {', '.join(allowed_audio_extensions)} 格式的文件"
            )

        # Save audio file to temp directory
        audio_file_path = os.path.join(temp_dir, f"uploaded_audio{audio_extension}")
        with open(audio_file_path, "wb") as buffer:
            shutil.copyfileobj(audio_file.file, buffer)
        logger.info(f"Audio file saved to: {audio_file_path}")

        # Update task status to processing
        update_task_status(db, task_uuid, "processing")

        # Check if video synthesizer is available
        if video_synthesizer is None:
            update_task_status(db, task_uuid, "failed", error_reason="视频合成器未初始化")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="视频合成服务暂时不可用"
            )

        # Start processing task in background (使用上传的音频文件)
        try:
            asyncio.create_task(
                video_synthesizer.queue_synthesis_task(
                    task_id=task_uuid,
                    model_id=None,  # 不需要模型ID
                    video_file_path=video_file_path,
                    audio_file_path=audio_file_path,
                    callback_url=""
                )
            )
            logger.info(f"任务 {task_uuid} 已提交到多线程视频合成器（使用上传音频）")
        except Exception as e:
            logger.error(f"提交任务到视频合成器失败: {str(e)}")
            update_task_status(db, task_uuid, "failed", error_reason=f"任务提交失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"任务提交失败: {str(e)}"
            )

        return StandardResponse(
            code=200,
            msg="ok",
            time=str(current_time),
            task_id=task_uuid
        )

    except Exception as e:
        logger.error(f"Error in video_generate_with_audio: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理失败: {str(e)}"
        )

# Video synthesizer management endpoints

@app.get("/api/video_synthesizer/status")
async def get_video_synthesizer_status(current_user: User = Depends(require_admin)):
    """获取视频合成器状态（管理员）"""
    global video_synthesizer

    if video_synthesizer is None:
        return {
            "code": 500,
            "msg": "视频合成器未初始化",
            "data": {
                "status": "not_initialized",
                "workers": 0,
                "start_time": start_time
            }
        }

    # 获取工作线程状态
    workers_status = []
    for i, worker in enumerate(video_synthesizer.workers):
        worker_status = {
            "worker_id": worker.worker_id,
            "queue_size": worker.task_queue.qsize(),
            "is_alive": worker.worker_thread.is_alive() if worker.worker_thread else False,
            "running": worker.running
        }
        workers_status.append(worker_status)

    return {
        "code": 200,
        "msg": "ok",
        "data": {
            "status": "running",
            "num_workers": len(video_synthesizer.workers),
            "workers": workers_status,
            "start_time": start_time,
            "config": get_video_synthesis_config()
        }
    }


@app.post("/api/video_synthesizer/reconfigure")
async def reconfigure_video_synthesizer(
    num_workers: int = Form(...),
    current_user: User = Depends(require_admin)
):
    """重新配置视频合成器工作线程数量（管理员）"""
    global video_synthesizer

    if num_workers < 1 or num_workers > 32:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="工作线程数量必须在1-32之间"
        )

    try:
        logger.info(f"管理员 {current_user.username} 请求重新配置视频合成器，工作线程数: {num_workers}")

        # 重新配置视频合成器
        set_video_synthesizer_workers(num_workers)
        video_synthesizer = get_video_synthesizer()

        logger.info(f"视频合成器重新配置成功，当前工作线程数: {len(video_synthesizer.workers)}")

        return {
            "code": 200,
            "msg": f"视频合成器重新配置成功，当前工作线程数: {len(video_synthesizer.workers)}",
            "data": {
                "num_workers": len(video_synthesizer.workers),
                "reconfigured_at": int(time.time())
            }
        }

    except Exception as e:
        logger.error(f"重新配置视频合成器失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重新配置失败: {str(e)}"
        )


# Download endpoints


@app.get("/models/{model_uuid}")
async def download_model(model_uuid: str, db: Session = Depends(get_db_with_pool)):
    """模型下载接口"""
    model = db.query(Model).filter(Model.model_uuid == model_uuid).first()
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型不存在"
        )

    audio_dir = os.path.join("audio", model_uuid)
    if not os.path.exists(audio_dir):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型文件不存在"
        )

    # Find audio file
    audio_file = None
    for file in os.listdir(audio_dir):
        if file.startswith("audio."):
            audio_file = file
            break

    if not audio_file:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="音频文件不存在"
        )

    file_path = os.path.join(audio_dir, audio_file)
    file_extension = os.path.splitext(audio_file)[1]
    filename = f"{model_uuid}{file_extension}"

    return FileResponse(
        path=file_path,
        filename=filename,
        media_type='application/octet-stream'
    )


@app.get("/video/{task_uuid}")
async def download_video(task_uuid: str, db: Session = Depends(get_db_with_pool)):
    """视频下载接口"""
    task = db.query(Task).filter(Task.task_uuid == task_uuid).first()
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="任务不存在"
        )

    # Video file is directly in result directory with task_uuid as filename
    video_file_path = os.path.join("result", f"{task_uuid}.mp4")

    if not os.path.exists(video_file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频文件不存在"
        )

    filename = f"{task_uuid}.mp4"

    return FileResponse(
        path=video_file_path,
        filename=filename,
        media_type='application/octet-stream'
    )

# Web page routes


@app.get("/")
async def index(request: Request, current_user: Optional[User] = Depends(get_optional_current_user)):
    """首页"""
    if current_user:
        if current_user.is_admin():
            return RedirectResponse(url="/admin/dashboard")
        else:
            return RedirectResponse(url="/user/dashboard")
    return RedirectResponse(url="/login")


@app.get("/login")
async def login_page(request: Request, current_user: Optional[User] = Depends(get_optional_current_user), db: Session = Depends(get_db_with_pool)):
    """登录页面"""
    # 如果已经登录，重定向到相应的仪表板
    if current_user:
        if current_user.is_admin():
            return RedirectResponse(url="/admin/dashboard")
        else:
            return RedirectResponse(url="/user/dashboard")

    app_name = get_app_name(db)
    return templates.TemplateResponse("login.html", {"request": request, "app_name": app_name})


@app.get("/admin/login")
async def admin_login_page(request: Request, current_user: Optional[User] = Depends(get_optional_current_user), db: Session = Depends(get_db_with_pool)):
    """管理员登录页面"""
    try:
        # 如果已经是管理员用户，重定向到仪表板
        if current_user and current_user.is_admin():
            return RedirectResponse(url="/admin/dashboard")

        # 确保不传递 user 参数，这样 base.html 会显示 login_content 块
        logger.info("渲染管理员登录页面")
        app_name = get_app_name(db)
        response = templates.TemplateResponse("admin_login.html", {"request": request, "app_name": app_name})
        logger.info("管理员登录页面渲染成功")
        return response
    except Exception as e:
        logger.error(f"管理员登录页面渲染失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"页面渲染失败: {str(e)}")



@app.get("/admin/dashboard")
async def admin_dashboard(request: Request, current_user: User = Depends(require_admin), db: Session = Depends(get_db_with_pool)):
    """管理员仪表板"""
    app_name = get_app_name(db)
    return templates.TemplateResponse("admin_dashboard.html", {
        "request": request,
        "user": current_user,
        "app_name": app_name
    })


@app.get("/admin/users")
async def admin_users_page(request: Request, current_user: User = Depends(require_admin), db: Session = Depends(get_db_with_pool)):
    """用户管理页面"""
    app_name = get_app_name(db)
    return templates.TemplateResponse("admin_users.html", {
        "request": request,
        "user": current_user,
        "app_name": app_name
    })


@app.get("/admin/models")
async def admin_models_page(request: Request, current_user: User = Depends(require_admin), db: Session = Depends(get_db_with_pool)):
    """模型管理页面（管理员）"""
    app_name = get_app_name(db)
    return templates.TemplateResponse("admin_models.html", {
        "request": request,
        "user": current_user,
        "app_name": app_name
    })


@app.get("/admin/tasks")
async def admin_tasks_page(request: Request, current_user: User = Depends(require_admin), db: Session = Depends(get_db_with_pool)):
    """任务管理页面（管理员）"""
    app_name = get_app_name(db)
    return templates.TemplateResponse("admin_tasks.html", {
        "request": request,
        "user": current_user,
        "app_name": app_name
    })


@app.get("/user/dashboard")
async def user_dashboard(request: Request, current_user: User = Depends(require_login), db: Session = Depends(get_db_with_pool)):
    """用户仪表板"""
    app_name = get_app_name(db)
    return templates.TemplateResponse("user_dashboard.html", {
        "request": request,
        "user": current_user,
        "app_name": app_name
    })


@app.get("/user/models")
async def user_models_page(request: Request, current_user: User = Depends(require_login), db: Session = Depends(get_db_with_pool)):
    """模型管理页面（用户）"""
    app_name = get_app_name(db)
    return templates.TemplateResponse("user_models.html", {
        "request": request,
        "user": current_user,
        "app_name": app_name
    })


@app.get("/user/tasks")
async def user_tasks_page(request: Request, current_user: User = Depends(require_login), db: Session = Depends(get_db_with_pool)):
    """任务管理页面（用户）"""
    app_name = get_app_name(db)
    return templates.TemplateResponse("user_tasks.html", {
        "request": request,
        "user": current_user,
        "app_name": app_name
    })


@app.get("/user/video_models")
async def user_video_models_page(request: Request, current_user: User = Depends(require_login), db: Session = Depends(get_db_with_pool)):
    """视频形象管理页面（用户）"""
    app_name = get_app_name(db)
    return templates.TemplateResponse("user_video_models.html", {
        "request": request,
        "user": current_user,
        "app_name": app_name
    })


@app.get("/admin/video_models")
async def admin_video_models_page(request: Request, current_user: User = Depends(require_admin), db: Session = Depends(get_db_with_pool)):
    """视频形象管理页面（管理员）"""
    app_name = get_app_name(db)
    return templates.TemplateResponse("admin_video_models.html", {
        "request": request,
        "user": current_user,
        "app_name": app_name
    })


@app.get("/admin/system_settings")
async def admin_system_settings_page(request: Request, current_user: User = Depends(require_admin), db: Session = Depends(get_db_with_pool)):
    """系统设置管理页面（管理员）"""
    app_name = get_app_name(db)
    return templates.TemplateResponse("admin_system_settings.html", {
        "request": request,
        "user": current_user,
        "app_name": app_name
    })


@app.get("/admin/video_synthesizer")
async def admin_video_synthesizer_page(request: Request, current_user: User = Depends(require_admin)):
    """视频合成器管理页面（管理员）"""
    return templates.TemplateResponse("admin_video_synthesizer.html", {
        "request": request,
        "user": current_user
    })


# Video Model Management Endpoints

@app.get("/api/video_models")
async def get_video_models(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """获取视频形象列表"""
    if current_user.user_type == "admin":
        video_models = get_all_video_models(db, skip, limit)
        total = get_all_video_models_count(db)
    else:
        video_models = get_video_models_by_user(
            db, current_user.id, skip, limit)
        total = get_video_models_count_by_user(db, current_user.id)

    return {
        "code": 200,
        "msg": "ok",
        "data": [
            {
                "id": video_model.id,
                "model_name": video_model.model_name,
                "model_uuid": video_model.model_uuid,
                "train_status": video_model.train_status,
                "download_url": video_model.download_url,
                "error_reason": video_model.error_reason,
                "created_time": video_model.created_time.isoformat(),
                "updated_time": video_model.updated_time.isoformat(),
                "owner": video_model.owner.nickname if current_user.user_type == "admin" else None,
                "preview_url": f"/video_models/{video_model.model_uuid}/preview" if video_model.train_status == "completed" else None
            }
            for video_model in video_models
        ],
        "total": total
    }


@app.post("/api/video_models")
async def create_video_model_api(
    request: VideoModelCreateRequest,
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """创建视频形象"""
    video_model = create_video_model(db, current_user.id, request.model_name)

    return {
        "code": 200,
        "msg": "视频形象创建成功",
        "data": {
            "id": video_model.id,
            "model_name": video_model.model_name,
            "model_uuid": video_model.model_uuid,
            "train_status": video_model.train_status
        }
    }


@app.put("/api/video_models/{model_uuid}")
async def update_video_model_api(
    model_uuid: str,
    request: VideoModelUpdateRequest,
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """更新视频形象"""
    # Check if video model exists and user has permission
    video_model = get_video_model_by_uuid(db, model_uuid)
    if not video_model:
        raise HTTPException(status_code=404, detail="视频形象不存在")

    # Check permission
    if current_user.user_type != "admin" and video_model.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权限操作此视频形象")

    updated_video_model = update_video_model_name(
        db, model_uuid, request.model_name)

    return {
        "code": 200,
        "msg": "视频形象更新成功",
        "data": {
            "id": updated_video_model.id,
            "model_name": updated_video_model.model_name,
            "model_uuid": updated_video_model.model_uuid,
            "train_status": updated_video_model.train_status
        }
    }


@app.delete("/api/video_models/{model_uuid}")
async def delete_video_model_api(
    model_uuid: str,
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """删除视频形象"""
    # Check if video model exists and user has permission
    video_model = get_video_model_by_uuid(db, model_uuid)
    if not video_model:
        raise HTTPException(status_code=404, detail="视频形象不存在")

    # Check permission
    if current_user.user_type != "admin" and video_model.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权限操作此视频形象")

    # Delete video files
    video_dir = os.path.join("videos", model_uuid)
    if os.path.exists(video_dir):
        try:
            shutil.rmtree(video_dir)
            logger.info(f"Deleted video directory: {video_dir}")
        except Exception as e:
            logger.warning(
                f"Failed to delete video directory {video_dir}: {str(e)}")

    # Delete from database
    success = delete_video_model(db, model_uuid)
    if not success:
        raise HTTPException(status_code=500, detail="删除失败")

    return {
        "code": 200,
        "msg": "视频形象删除成功"
    }


@app.post("/api/video_train")
async def video_train_api(
    model_uuid: str = Form(...),
    video_file: UploadFile = File(...),
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """视频训练接口"""

    # Check if video model exists and user has permission
    video_model = get_video_model_by_uuid(db, model_uuid)
    if not video_model:
        raise HTTPException(status_code=404, detail="视频形象不存在")

    # Check permission
    if current_user.user_type != "admin" and video_model.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权限操作此视频形象")

    try:
        # Validate video file
        if not video_file.filename:
            raise HTTPException(status_code=400, detail="未选择视频文件")

        # Check file size (limit to 100MB)
        content = await video_file.read()
        if len(content) == 0:
            raise HTTPException(status_code=400, detail="视频文件为空")

        if len(content) > 1000 * 1024 * 1024:  # 100MB
            raise HTTPException(status_code=400, detail="视频文件过大，请上传小于100MB的文件")

        # Check file extension
        file_extension = os.path.splitext(video_file.filename)[1].lower()
        allowed_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm']
        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的视频格式，请上传 {', '.join(allowed_extensions)} 格式的文件"
            )

        # Create video directory
        video_dir = os.path.join("videos", model_uuid)
        os.makedirs(video_dir, exist_ok=True)

        # Save video file to temporary location first
        temp_video_path = os.path.join(video_dir, f"temp{file_extension}")
        with open(temp_video_path, "wb") as buffer:
            buffer.write(content)

        logger.info(f"Temporary video file saved: {temp_video_path}")

        # 清除旋转属性
        final_video_path = os.path.join(video_dir, f"source{file_extension}")
        success = process_video_with_ffmpeg(temp_video_path, final_video_path)

        if not success:
            # 如果处理失败，直接使用原文件
            logger.warning("FFmpeg处理失败，使用原始视频文件")
            shutil.move(temp_video_path, final_video_path)

        # 删除临时文件
        try:
            if os.path.exists(temp_video_path):
                os.remove(temp_video_path)
                logger.info(f"Temporary video file deleted: {temp_video_path}")
        except Exception as e:
            logger.warning(f"Failed to delete temporary file {temp_video_path}: {str(e)}")

        logger.info(f"Final video file saved: {final_video_path}")

        # 提取视频帧保存为 view.png
        view_image_path = os.path.join(video_dir, "view.png")
        try:
            import cv2
            # 打开视频文件
            cap = cv2.VideoCapture(final_video_path)
            if cap.isOpened():
                # 读取第一帧
                ret, frame = cap.read()
                if ret:
                    # 保存为 view.png
                    cv2.imwrite(view_image_path, frame)
                    logger.info(f"视频帧已提取并保存为: {view_image_path}")
                else:
                    logger.warning("无法读取视频帧")
                cap.release()
            else:
                logger.warning(f"无法打开视频文件: {final_video_path}")
        except Exception as e:
            logger.error(f"提取视频帧失败: {str(e)}")

        # 提取音频保存为 audio.mp3
        try:
            audio_path = extract_audio_from_video(final_video_path, video_dir, max_duration=10.0)
            logger.info(f"音频已提取并保存为: {audio_path}")
        except Exception as e:
            logger.error(f"提取音频失败: {str(e)}")
            # 音频提取失败不影响整个流程，继续执行

        # Update model status to training
        update_video_model_status(db, model_uuid, "training")

        # Set download URL
        download_url = f"/video_models/{model_uuid}"

        # Update model with download URL and completed status
        # In a real implementation, you would start actual training here
        # For now, we'll just mark it as completed
        update_video_model_status(db, model_uuid, "completed", download_url," ")

        current_time = int(time.time())

        return {
            "code": 200,
            "msg": "ok",
            "time": str(current_time),
            "model_id": model_uuid,
        }

    except Exception as e:
        logger.error(f"Video training failed: {str(e)}")
        update_video_model_status(
            db, model_uuid, "failed", error_reason=str(e))
        raise HTTPException(status_code=500, detail=f"训练失败: {str(e)}")


@app.get("/video_models/{model_uuid}")
async def download_video_model(model_uuid: str, db: Session = Depends(get_db_with_pool)):
    """视频形象下载接口"""
    video_model = get_video_model_by_uuid(db, model_uuid)
    if not video_model:
        raise HTTPException(status_code=404, detail="视频形象不存在")

    if video_model.train_status != "completed":
        raise HTTPException(status_code=400, detail="视频形象训练未完成")

    # Find the source video file
    video_dir = os.path.join("videos", model_uuid)
    if not os.path.exists(video_dir):
        raise HTTPException(status_code=404, detail="视频文件不存在")

    # Look for source file with any extension
    source_files = [f for f in os.listdir(
        video_dir) if f.startswith("source.")]
    if not source_files:
        raise HTTPException(status_code=404, detail="源视频文件不存在")

    video_path = os.path.join(video_dir, source_files[0])

    if not os.path.exists(video_path):
        raise HTTPException(status_code=404, detail="视频文件不存在")

    return FileResponse(
        video_path,
        media_type="video/mp4",
        filename=f"{video_model.model_name}.mp4"
    )


@app.get("/video_models/{model_uuid}/preview")
async def get_video_model_preview(model_uuid: str, db: Session = Depends(get_db_with_pool)):
    """获取视频形象预览图片"""
    video_model = get_video_model_by_uuid(db, model_uuid)
    if not video_model:
        raise HTTPException(status_code=404, detail="视频形象不存在")

    if video_model.train_status != "completed":
        raise HTTPException(status_code=400, detail="视频形象训练未完成")

    # 查找预览图片文件
    video_dir = os.path.join("videos", model_uuid)
    preview_path = os.path.join(video_dir, "view.png")

    if not os.path.exists(preview_path):
        raise HTTPException(status_code=404, detail="预览图片不存在")

    return FileResponse(
        path=preview_path,
        filename=f"{video_model.model_name}_preview.png",
        media_type='image/png'
    )


@app.post("/api/video_gene")
async def video_generate_api(
    request: VideoGenerateRequest,
    current_user: User = Depends(require_login),
    db: Session = Depends(get_db_with_pool)
):
    """视频合成接口"""
    try:
        # 首先检查任务是否存在
        task = db.query(Task).filter(Task.task_uuid == request.task_uuid).first()
        if not task:
            return {
                "code": 404,
                "msg": "任务不存在"
            }

        # 检查任务权限
        if not check_task_permission(current_user, task.user_id):
            return {
                "code": 403,
                "msg": "无权限操作此任务"
            }

        # 检查任务状态
        if task.task_status != "new":
            return {
                "code": 400,
                "msg": f"任务状态不正确，当前状态: {task.task_status}"
            }

        # Check if audio model exists (only if provided)
        audio_model = None
        if request.model_uuid:
            audio_model = db.query(Model).filter(
                Model.model_uuid == request.model_uuid).first()
            if not audio_model:
                return {
                    "code": 404,
                    "msg": "音频模型不存在"
                }

        # Check if video model exists
        video_model = get_video_model_by_uuid(db, request.video_model_uuid)
        if not video_model:
            return {
                "code": 404,
                "msg": "视频形象不存在"
            }

        # Check if video model is completed
        if video_model.train_status != "completed":
            return {
                "code": 400,
                "msg": "视频形象训练未完成"
            }

        # Check permissions
        if current_user.user_type != "admin":
            if audio_model and audio_model.user_id != current_user.id:
                return {
                    "code": 403,
                    "msg": "无权限使用此音频模型"
                }
            if video_model.user_id != current_user.id:
                return {
                    "code": 403,
                    "msg": "无权限使用此视频形象"
                }

        # Get video file path
        video_dir = os.path.join("videos", request.video_model_uuid)
        if not os.path.exists(video_dir):
            return {
                "code": 404,
                "msg": "视频形象文件目录不存在"
            }

        source_files = [f for f in os.listdir(video_dir) if f.startswith("source.")]
        if not source_files:
            return {
                "code": 404,
                "msg": "视频源文件不存在"
            }

        video_file_path = os.path.abspath(os.path.join(video_dir, source_files[0]))

        # Update task status to processing immediately
        update_task_status(db, task.task_uuid, "processing")

    
        asyncio.create_task(
            video_synthesizer.queue_synthesis_task(
                task_id=task.task_uuid,
                model_id=request.model_uuid,
                video_file_path=video_file_path,
                content=request.content,
                callback_url=""
            )
        )

        # Return immediately without waiting for synthesis to complete
        return {
            "code": 200,
            "msg": "合成任务已提交",
            "data": {
                "task_uuid": task.task_uuid,
                "task_name": task.task_name,
                "status": "processing"
            }
        }

    except Exception as e:
        logger.error(f"Video generation failed: {str(e)}")
        return {
            "code": 500,
            "msg": f"合成失败: {str(e)}"
        }

def get_app_name(db: Session) -> str:
    """获取应用名称"""
    setting = get_system_setting(db, "app_name")
    return setting.setting_value if setting else "HeyGem"


def process_video_with_ffmpeg(input_video, output_video):
        """移除旋转元数据但保持视频内容方向不变"""
        try:
            # 获取视频信息
            probe = ffmpeg.probe(input_video)
            has_audio = any(stream['codec_type'] == 'audio' for stream in probe['streams'])
            
            # 构建FFmpeg命令
            input_stream = ffmpeg.input(input_video)
            
            # 输出参数 - 关键点：只移除旋转元数据，不修改视频内容
            output_args = {
                'c:v': 'libx264',
                'preset': 'veryfast',
                'pix_fmt': 'yuv420p',
                'metadata:s:v': 'rotate=0'  # 强制清除旋转标签
            }
            
            # 如果有音频则保留
            if has_audio:
                output = ffmpeg.output(
                    input_stream.video,
                    input_stream.audio,
                    output_video,
                    **output_args
                )
            else:
                output = ffmpeg.output(
                    input_stream.video,
                    output_video,
                    **output_args
                )
            
            # 运行命令
            process = output.run_async(overwrite_output=True, pipe_stderr=True)
            _, stderr = process.communicate()
            
            if process.returncode != 0:
                raise ffmpeg.Error('ffmpeg', None, stderr)
            
            logger.info(f"已生成无旋转标签的新视频: {output_video}")
            return True
        
        except ffmpeg.Error as e:
            error_msg = e.stderr.decode('utf8') if e.stderr else str(e)
            logger.error(f"FFmpeg处理失败: {error_msg}")
            return False
        except Exception as e:
            logger.error(f"视频处理失败: {str(e)}")
            return False


# System Settings API
@app.get("/api/system_settings")
async def get_system_settings(
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db_with_pool)
):
    """获取所有系统设置（管理员）"""
    settings = get_all_system_settings(db)
    return {
        "code": 200,
        "msg": "ok",
        "data": [
            {
                "setting_key": setting.setting_key,
                "setting_value": setting.setting_value,
                "setting_type": setting.setting_type,
                "description": setting.description,
                "created_time": setting.created_time.isoformat(),
                "updated_time": setting.updated_time.isoformat()
            }
            for setting in settings
        ]
    }


@app.get("/api/system_settings/{setting_key}")
async def get_system_setting_by_key(
    setting_key: str,
    db: Session = Depends(get_db_with_pool)
):
    """获取指定的系统设置（公开接口）"""
    setting = get_system_setting(db, setting_key)
    if not setting:
        raise HTTPException(status_code=404, detail="设置不存在")

    return {
        "code": 200,
        "msg": "ok",
        "data": {
            "setting_key": setting.setting_key,
            "setting_value": setting.setting_value,
            "setting_type": setting.setting_type,
            "description": setting.description
        }
    }


@app.post("/api/system_settings")
async def create_or_update_system_setting_api(
    request: SystemSettingRequest,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db_with_pool)
):
    """创建或更新系统设置（管理员）"""
    setting = create_or_update_system_setting(
        db,
        request.setting_key,
        request.setting_value,
        request.setting_type,
        request.description
    )

    return {
        "code": 200,
        "msg": "设置保存成功",
        "data": {
            "setting_key": setting.setting_key,
            "setting_value": setting.setting_value,
            "setting_type": setting.setting_type,
            "description": setting.description
        }
    }


@app.delete("/api/system_settings/{setting_key}")
async def delete_system_setting_api(
    setting_key: str,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db_with_pool)
):
    """删除系统设置（管理员）"""
    success = delete_system_setting(db, setting_key)
    if not success:
        raise HTTPException(status_code=404, detail="设置不存在")

    return {
        "code": 200,
        "msg": "设置删除成功"
    }


# if __name__ == "__main__":
#     import uvicorn
#     uvicorn.run(app, host="0.0.0.0", port=8880)
# Application entry point
if __name__ == "__main__":
    import uvicorn
    # Use single worker to avoid multiprocessing issues
    uvicorn.run(app, host="0.0.0.0", port=8880)
    # uvicorn.run(
    #     "management_api:app", 
    #     host="0.0.0.0", 
    #     port=8000, 
    #     workers=2,
    #     limit_concurrency=20,
    # )
