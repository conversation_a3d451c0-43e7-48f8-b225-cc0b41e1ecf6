import os
import uuid
from datetime import datetime, timedelta
from typing import Optional, List
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Enum, ForeignKey, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship
from sqlalchemy.dialects.mysql import CHAR
import configparser
from passlib.context import CryptContext
try:
    from y_utils.logger import logger
except ImportError:
    from simple_logger import logger

# Read database configuration
config = configparser.ConfigParser()
config.read('config/config.ini')

# Database configuration
DB_HOST = config.get('DATABASE', 'host', fallback='localhost')
DB_PORT = config.getint('DATABASE', 'port', fallback=3306)
DB_USER = config.get('DATABASE', 'user', fallback='root')
DB_PASSWORD = config.get('DATABASE', 'password', fallback='')
DB_NAME = config.get('DATABASE', 'name', fallback='heygem_management')

# Create database URL
DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?charset=utf8mb4"

# Create engine with connection pool configuration
engine = create_engine(
    DATABASE_URL,
    echo=False,
    pool_pre_ping=True,
    pool_size=10,          # 连接池大小
    max_overflow=20,       # 最大溢出连接数
    pool_timeout=30,       # 获取连接超时时间
    pool_recycle=3600      # 连接回收时间（1小时）
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class
Base = declarative_base()

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    nickname = Column(String(100), nullable=False)
    password_hash = Column(String(255), nullable=False)
    expire_time = Column(DateTime, nullable=True)  # admin账号为None表示永不失效
    created_time = Column(DateTime, default=datetime.now)
    updated_time = Column(DateTime, default=datetime.now,
                          onupdate=datetime.now)
    user_type = Column(
        Enum('admin', 'user', name='user_type_enum'), nullable=False, default='user')

    updated_time = Column(DateTime, default=datetime.now,
                          onupdate=datetime.now)
    user_type = Column(
        Enum('admin', 'user', name='user_type_enum'), nullable=False, default='user')

    # Relationships
    models = relationship("Model", back_populates="owner",
                          cascade="all, delete-orphan")
    video_models = relationship("VideoModel", back_populates="owner",
                                cascade="all, delete-orphan")
    tasks = relationship("Task", back_populates="owner",
                         cascade="all, delete-orphan")

    def verify_password(self, password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(password, self.password_hash)

    def set_password(self, password: str):
        """设置密码"""
        self.password_hash = pwd_context.hash(password)

    def is_expired(self) -> bool:
        """检查用户是否已过期"""
        if self.expire_time is None:  # admin账号永不过期
            return False
        return datetime.now() > self.expire_time

    def is_admin(self) -> bool:
        """检查是否为管理员"""
        return self.user_type == 'admin'


class Model(Base):
    __tablename__ = "models"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    model_name = Column(String(200), nullable=False)
    audio_file_path = Column(String(500), nullable=True)  # 音频文件路径
    download_url = Column(String(500), nullable=True)
    model_uuid = Column(CHAR(36), unique=True, index=True,
                        nullable=False, default=lambda: str(uuid.uuid4()))
    model_uuid = Column(CHAR(36), unique=True, index=True,
                        nullable=False, default=lambda: str(uuid.uuid4()))
    train_status = Column(Enum('new', 'training', 'completed', 'failed', name='train_status_enum'),
                          nullable=False, default='new')
    error_reason = Column(Text, nullable=True)
    created_time = Column(DateTime, default=datetime.now)
    updated_time = Column(DateTime, default=datetime.now,
                          onupdate=datetime.now)
    updated_time = Column(DateTime, default=datetime.now,
                          onupdate=datetime.now)

    # Relationships
    owner = relationship("User", back_populates="models")


class VideoModel(Base):
    __tablename__ = "video_models"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    model_name = Column(String(200), nullable=False)
    video_file_path = Column(String(500), nullable=True)  # 视频文件路径
    download_url = Column(String(500), nullable=True)
    model_uuid = Column(CHAR(36), unique=True, index=True,
                        nullable=False, default=lambda: str(uuid.uuid4()))
    train_status = Column(Enum('new', 'training', 'completed', 'failed', name='video_train_status_enum'),
                          nullable=False, default='new')
    error_reason = Column(Text, nullable=True)
    created_time = Column(DateTime, default=datetime.now)
    updated_time = Column(DateTime, default=datetime.now,
                          onupdate=datetime.now)

    # Relationships
    owner = relationship("User", back_populates="video_models")


class SystemSetting(Base):
    __tablename__ = "system_settings"

    id = Column(Integer, primary_key=True, index=True)
    setting_key = Column(String(100), unique=True, nullable=False, index=True)
    setting_value = Column(Text, nullable=True)
    setting_type = Column(String(50), nullable=False, default='text')  # text, image, json
    description = Column(String(500), nullable=True)
    created_time = Column(DateTime, default=datetime.now)
    updated_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class Task(Base):
    __tablename__ = "tasks"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    model_uuid = Column(CHAR(36), nullable=True)  # 关联的音频模型UUID
    video_model_uuid = Column(CHAR(36), nullable=True)  # 关联的视频形象UUID
    task_name = Column(String(200), nullable=False)
    content = Column(Text, nullable=True)  # 文案内容
    download_url = Column(String(500), nullable=True)
    task_uuid = Column(CHAR(36), unique=True, index=True,
                       nullable=False, default=lambda: str(uuid.uuid4()))
    task_status = Column(Enum('new', 'processing', 'completed', 'failed', name='task_status_enum'),
                         nullable=False, default='new')
    error_reason = Column(Text, nullable=True)
    created_time = Column(DateTime, default=datetime.now)
    updated_time = Column(DateTime, default=datetime.now,
                          onupdate=datetime.now)

    # Relationships
    owner = relationship("User", back_populates="tasks")


def get_db() -> Session:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_db_session() -> Session:
    """获取数据库会话（直接返回Session对象，用于线程池管理）"""
    return SessionLocal()


def create_database_indexes():
    """创建数据库索引以提高查询性能"""
    try:
        db = SessionLocal()
        try:
            # 创建索引的SQL语句（兼容MySQL 5.7+）
            indexes = [
                ("idx_tasks_user_id_created_time",
                 "CREATE INDEX idx_tasks_user_id_created_time ON tasks(user_id, created_time DESC)"),
                ("idx_tasks_created_time",
                 "CREATE INDEX idx_tasks_created_time ON tasks(created_time DESC)"),
                ("idx_tasks_status",
                 "CREATE INDEX idx_tasks_status ON tasks(task_status)"),
                ("idx_tasks_status_updated",
                 "CREATE INDEX idx_tasks_status_updated ON tasks(task_status, updated_time DESC)"),
                ("idx_tasks_user_status_updated",
                 "CREATE INDEX idx_tasks_user_status_updated ON tasks(user_id, task_status, updated_time DESC)"),
                ("idx_models_user_id_created_time",
                 "CREATE INDEX idx_models_user_id_created_time ON models(user_id, created_time DESC)"),
                ("idx_models_train_status",
                 "CREATE INDEX idx_models_train_status ON models(train_status)"),
                ("idx_models_status_updated",
                 "CREATE INDEX idx_models_status_updated ON models(train_status, updated_time DESC)"),
                ("idx_models_user_status_updated",
                 "CREATE INDEX idx_models_user_status_updated ON models(user_id, train_status, updated_time DESC)"),
                ("idx_users_user_type",
                 "CREATE INDEX idx_users_user_type ON users(user_type)")
            ]

            for index_name, index_sql in indexes:
                try:
                    # 先检查索引是否存在
                    check_sql = f"""
                        SELECT COUNT(*) FROM information_schema.statistics
                        WHERE table_schema = DATABASE()
                        AND table_name = '{index_sql.split(' ON ')[1].split('(')[0]}'
                        AND index_name = '{index_name}'
                    """
                    result = db.execute(text(check_sql)).scalar()

                    if result == 0:
                        db.execute(text(index_sql))
                        logger.info(f"Index created: {index_name}")
                    else:
                        logger.info(f"Index already exists: {index_name}")
                except Exception as e:
                    logger.warning(
                        f"Index creation failed for {index_name}: {str(e)}")

            db.commit()
            logger.info("Database indexes creation process completed")
        finally:
            db.close()
    except Exception as e:
        logger.error(f"Database index creation failed: {str(e)}")


def init_database():
    """初始化数据库"""
    try:
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")

        # Create indexes for better performance
        create_database_indexes()

        # Create default admin user
        db = SessionLocal()
        try:
            admin_user = db.query(User).filter(
                User.username == "admin").first()
            if not admin_user:
                admin_user = User(
                    username="admin",
                    nickname="系统管理员",
                    user_type="admin",
                    expire_time=None  # 永不失效
                )
                admin_user.set_password("admin123")  # 默认密码
                db.add(admin_user)
                db.commit()
                logger.info("Default admin user created: admin/admin123")
            else:
                logger.info("Admin user already exists")
        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise


def get_user_by_username(db: Session, username: str) -> Optional[User]:
    """根据用户名获取用户"""
    return db.query(User).filter(User.username == username).first()


def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
    """根据ID获取用户"""
    return db.query(User).filter(User.id == user_id).first()


def create_user(db: Session, username: str, nickname: str, password: str,
                user_type: str = "user", expire_days: int = 30) -> User:
    """创建用户"""
    user = User(
        username=username,
        nickname=nickname,
        user_type=user_type,
        expire_time=datetime.now() + timedelta(days=expire_days) if user_type != "admin" else None
    )
    user.set_password(password)
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


def update_user(db: Session, user_id: int, nickname: str = None, password: str = None,
                expire_time: datetime = None) -> Optional[User]:
    """更新用户信息"""
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        return None

    if nickname is not None:
        user.nickname = nickname
    if password is not None:
        user.set_password(password)
    if expire_time is not None:
        user.expire_time = expire_time

    user.updated_time = datetime.now()
    db.commit()
    db.refresh(user)
    return user


def get_models_by_user(db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[Model]:
    """获取用户的模型列表"""
    return db.query(Model).filter(Model.user_id == user_id).offset(skip).limit(limit).all()


def get_all_models(db: Session, skip: int = 0, limit: int = 100) -> List[Model]:
    """获取所有模型列表（管理员用）"""
    return db.query(Model).offset(skip).limit(limit).all()


def get_completed_models_by_user(db: Session, user_id: int, limit: int = 50) -> List[Model]:
    """获取用户的已完成模型列表（优化版）"""
    return db.query(Model).filter(
        Model.user_id == user_id,
        Model.train_status == 'completed'
    ).order_by(Model.updated_time.desc()).limit(limit).all()


def get_all_completed_models(db: Session, limit: int = 100) -> List[Model]:
    """获取所有已完成模型列表（管理员用，优化版）"""
    from sqlalchemy.orm import joinedload
    return db.query(Model).options(joinedload(Model.owner)).filter(
        Model.train_status == 'completed'
    ).order_by(Model.updated_time.desc()).limit(limit).all()


def create_model(db: Session, user_id: int, model_name: str) -> Model:
    """创建模型"""
    model = Model(
        user_id=user_id,
        model_name=model_name
    )
    db.add(model)
    db.commit()
    db.refresh(model)
    return model


def get_tasks_by_user(db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[Task]:
    """获取用户的任务列表"""
    return db.query(Task).filter(Task.user_id == user_id).order_by(Task.created_time.desc()).offset(skip).limit(limit).all()


def get_all_tasks(db: Session, skip: int = 0, limit: int = 100) -> List[Task]:
    """获取所有任务列表（管理员用）"""
    from sqlalchemy.orm import joinedload
    return db.query(Task).options(joinedload(Task.owner)).order_by(Task.created_time.desc()).offset(skip).limit(limit).all()


def get_tasks_count_by_user(db: Session, user_id: int) -> int:
    """获取用户任务总数"""
    return db.query(Task).filter(Task.user_id == user_id).count()


def get_all_tasks_count(db: Session) -> int:
    """获取所有任务总数"""
    return db.query(Task).count()


def create_task(db: Session, user_id: int, task_name: str, content: str = None,
                model_uuid: str = None, video_model_uuid: str = None) -> Task:
    """创建任务"""
    task = Task(
        user_id=user_id,
        task_name=task_name,
        content=content,
        model_uuid=model_uuid,
        video_model_uuid=video_model_uuid
    )
    db.add(task)
    db.commit()
    db.refresh(task)
    return task


# Video Model related functions
def get_video_models_by_user(db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[VideoModel]:
    """获取用户的视频形象列表"""
    return db.query(VideoModel).filter(VideoModel.user_id == user_id).order_by(VideoModel.created_time.desc()).offset(skip).limit(limit).all()


def get_all_video_models(db: Session, skip: int = 0, limit: int = 100) -> List[VideoModel]:
    """获取所有视频形象列表（管理员用）"""
    from sqlalchemy.orm import joinedload
    return db.query(VideoModel).options(joinedload(VideoModel.owner)).order_by(VideoModel.created_time.desc()).offset(skip).limit(limit).all()


def get_completed_video_models_by_user(db: Session, user_id: int, limit: int = 50) -> List[VideoModel]:
    """获取用户的已完成视频形象列表"""
    return db.query(VideoModel).filter(
        VideoModel.user_id == user_id,
        VideoModel.train_status == 'completed'
    ).order_by(VideoModel.updated_time.desc()).limit(limit).all()


def get_all_completed_video_models(db: Session, limit: int = 100) -> List[VideoModel]:
    """获取所有已完成视频形象列表（管理员用）"""
    from sqlalchemy.orm import joinedload
    return db.query(VideoModel).options(joinedload(VideoModel.owner)).filter(
        VideoModel.train_status == 'completed'
    ).order_by(VideoModel.updated_time.desc()).limit(limit).all()


def get_video_models_count_by_user(db: Session, user_id: int) -> int:
    """获取用户视频形象总数"""
    return db.query(VideoModel).filter(VideoModel.user_id == user_id).count()


def get_all_video_models_count(db: Session) -> int:
    """获取所有视频形象总数"""
    return db.query(VideoModel).count()


def create_video_model(db: Session, user_id: int, model_name: str) -> VideoModel:
    """创建视频形象"""
    video_model = VideoModel(
        user_id=user_id,
        model_name=model_name
    )
    db.add(video_model)
    db.commit()
    db.refresh(video_model)
    return video_model


def get_video_model_by_uuid(db: Session, model_uuid: str) -> Optional[VideoModel]:
    """根据UUID获取视频形象"""
    return db.query(VideoModel).filter(VideoModel.model_uuid == model_uuid).first()


def update_video_model_status(db: Session, model_uuid: str, status: str,
                              download_url: str = None, error_reason: str = None) -> Optional[VideoModel]:
    """更新视频形象状态"""
    video_model = db.query(VideoModel).filter(
        VideoModel.model_uuid == model_uuid).first()
    if video_model:
        video_model.train_status = status
        if download_url is not None:
            video_model.download_url = download_url
        if error_reason is not None:
            video_model.error_reason = error_reason
        video_model.updated_time = datetime.now()
        db.commit()
        db.refresh(video_model)
    return video_model


def update_video_model_name(db: Session, model_uuid: str, model_name: str) -> Optional[VideoModel]:
    """更新视频形象名称"""
    video_model = db.query(VideoModel).filter(
        VideoModel.model_uuid == model_uuid).first()
    if video_model:
        video_model.model_name = model_name
        video_model.updated_time = datetime.now()
        db.commit()
        db.refresh(video_model)
    return video_model


def delete_video_model(db: Session, model_uuid: str) -> bool:
    """删除视频形象"""
    video_model = db.query(VideoModel).filter(
        VideoModel.model_uuid == model_uuid).first()
    if video_model:
        db.delete(video_model)
        db.commit()
        return True
    return False


# System Settings related functions
def get_system_setting(db: Session, setting_key: str) -> Optional[SystemSetting]:
    """获取系统设置"""
    return db.query(SystemSetting).filter(SystemSetting.setting_key == setting_key).first()


def get_all_system_settings(db: Session) -> List[SystemSetting]:
    """获取所有系统设置"""
    return db.query(SystemSetting).order_by(SystemSetting.setting_key).all()


def create_or_update_system_setting(db: Session, setting_key: str, setting_value: str,
                                   setting_type: str = 'text', description: str = None) -> SystemSetting:
    """创建或更新系统设置"""
    setting = db.query(SystemSetting).filter(SystemSetting.setting_key == setting_key).first()

    if setting:
        # 更新现有设置
        setting.setting_value = setting_value
        setting.setting_type = setting_type
        if description is not None:
            setting.description = description
        setting.updated_time = datetime.now()
    else:
        # 创建新设置
        setting = SystemSetting(
            setting_key=setting_key,
            setting_value=setting_value,
            setting_type=setting_type,
            description=description
        )
        db.add(setting)

    db.commit()
    db.refresh(setting)
    return setting


def delete_system_setting(db: Session, setting_key: str) -> bool:
    """删除系统设置"""
    setting = db.query(SystemSetting).filter(SystemSetting.setting_key == setting_key).first()
    if setting:
        db.delete(setting)
        db.commit()
        return True
    return False


def update_model_status(db: Session, model_uuid: str, status: str,
                        download_url: str = None, error_reason: str = None) -> Optional[Model]:
    """更新模型状态"""
    model = db.query(Model).filter(Model.model_uuid == model_uuid).first()
    if model:
        model.train_status = status
        if download_url:
            model.download_url = download_url
        if error_reason:
            model.error_reason = error_reason
        model.updated_time = datetime.now()
        db.commit()
        db.refresh(model)
    return model


def update_task_status(db: Session, task_uuid: str, status: str,
                       download_url: str = None, error_reason: str = None) -> Optional[Task]:
    """更新任务状态"""
    task = db.query(Task).filter(Task.task_uuid == task_uuid).first()
    if task:
        task.task_status = status
        if download_url:
            task.download_url = download_url
        if error_reason:
            task.error_reason = error_reason
        task.updated_time = datetime.now()
        db.commit()
        db.refresh(task)
    return task


def reset_processing_tasks():
    """应用重启时，将处理中的任务标记为失败"""
    db = SessionLocal()
    try:
        # 重置训练中的模型
        training_models = db.query(Model).filter(
            Model.train_status == 'training').all()
        for model in training_models:
            model.train_status = 'failed'
            model.error_reason = '应用重启，训练中断'
            model.updated_time = datetime.now()

        # 重置处理中的任务
        processing_tasks = db.query(Task).filter(
            Task.task_status == 'processing').all()
        for task in processing_tasks:
            task.task_status = 'failed'
            task.error_reason = '应用重启，合成中断'
            task.updated_time = datetime.now()

        db.commit()
        logger.info(
            f"Reset {len(training_models)} training models and {len(processing_tasks)} processing tasks")
    except Exception as e:
        logger.error(f"Error resetting processing tasks: {str(e)}")
        db.rollback()
    finally:
        db.close()
